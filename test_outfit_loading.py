#!/usr/bin/env python3
"""
Test script to verify the outfit data loading system works correctly.
Run this to check that the game will load outfit data from JSON files.
"""

import sys
import os
import json
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

def test_outfit_loading():
    """Test that outfit loading works correctly."""
    print("=" * 60)
    print("Testing Escape Velocity Py Outfit Data Loading System")
    print("=" * 60)
    
    try:
        # Import the game systems
        from game_objects.standardized_outfits import OUTFITS_REGISTRY
        print(f"✓ Successfully imported outfit system")
        
        # Check if outfits were loaded
        outfit_count = len(OUTFITS_REGISTRY)
        print(f"✓ Found {outfit_count} outfits in registry")
        
        if outfit_count == 0:
            print("⚠ No outfits found! This might indicate a problem.")
            return False
            
        # Check if we have the laser cannon with the correct data
        if "laser_cannon" in OUTFITS_REGISTRY:
            laser = OUTFITS_REGISTRY["laser_cannon"]
            print(f"✓ Found laser cannon: {laser.name}")
            print(f"  - Cost: {laser.cost} credits")
            print(f"  - Damage: {laser.damage}")
            print(f"  - Fire Rate: {laser.fire_rate}")
            print(f"  - Range: {laser.range}")
            
            # Check if JSON data exists
            json_file = "outfits_data.json"
            if os.path.exists(json_file):
                print(f"✓ Found JSON data file: {json_file}")
                
                with open(json_file, 'r') as f:
                    json_data = json.load(f)
                
                if "laser_cannon" in json_data:
                    json_cost = json_data["laser_cannon"]["cost"]
                    print(f"  - JSON file cost: {json_cost} credits")
                    
                    if laser.cost == json_cost:
                        print("✅ SUCCESS: Game outfit matches JSON data!")
                        print("   Editor changes will be reflected in the game.")
                    else:
                        print("❌ MISMATCH: Game outfit cost doesn't match JSON!")
                        print(f"   Game has: {laser.cost}, JSON has: {json_cost}")
                        print("   The game is still using hardcoded values.")
                else:
                    print("⚠ Laser cannon not found in JSON file")
            else:
                print(f"⚠ JSON file not found: {json_file}")
        else:
            print("⚠ Laser cannon not found in outfit registry")
            
        # Show some outfit statistics
        categories = {}
        for outfit in OUTFITS_REGISTRY.values():
            cat = outfit.category
            if cat not in categories:
                categories[cat] = 0
            categories[cat] += 1
            
        print(f"\n📊 Outfit Categories:")
        for category, count in categories.items():
            print(f"  - {category}: {count} items")
            
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import game systems: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main test function."""
    success = test_outfit_loading()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Test completed! The outfit data loading system appears to be working.")
        print("\nNext steps:")
        print("1. Start the game and check the outfitter")
        print("2. Use the enhanced editor to modify outfit prices")
        print("3. Restart the game to see changes")
    else:
        print("❌ Test failed! There may be issues with the outfit loading system.")
        print("\nCheck the error messages above for details.")
    print("=" * 60)

if __name__ == "__main__":
    main()
