"""
Test script to verify that sprite loading is working correctly.
"""

import os
import sys
import pygame as pg
from PIL import Image, ImageTk

# Add the src directory to the path so we can import game modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import game modules
try:
    from game_objects.sprite_manager import SpriteSheet, load_sprite
except ImportError as e:
    print(f"Error importing game modules: {e}")
    print("Make sure you're running this from the EscapeVelocityPy directory.")
    sys.exit(1)

def print_separator():
    print("-" * 80)

def test_ship_sprites():
    """Test loading ship sprites."""
    print("Testing ship sprites...")
    
    # List of ship IDs to test
    ship_ids = ["scout", "light_fighter", "interceptor", "courier", "heavy_fighter", 
                "freighter", "passenger_liner", "gunship", "heavy_freighter", "corvette"]
    
    # Test each ship in each size category
    for size in ["small", "medium", "large", "capital"]:
        print(f"Testing {size} ships:")
        for ship_id in ship_ids:
            print(f"  Loading {ship_id}...")
            sprite = load_sprite("ship", ship_id, size)
            if sprite and sprite.image:
                print(f"    ✓ Successfully loaded {ship_id} ({size})")
                print(f"      Frame count: {sprite.frame_count}")
                print(f"      Sprite size: {sprite.sprite_size}")
            else:
                print(f"    ✗ Failed to load {ship_id} ({size})")
    
    print_separator()

def test_planet_sprites():
    """Test loading planet sprites."""
    print("Testing planet sprites...")
    
    # List of planet types to test
    planet_types = ["terrestrial", "desert", "ice", "gas_giant", "ocean", "volcanic", "barren", "toxic"]
    
    for planet_type in planet_types:
        print(f"Testing {planet_type} planets:")
        for variant in range(1, 4):  # Test variants 1-3
            planet_id = f"{planet_type}_{variant}"
            print(f"  Loading {planet_id}...")
            sprite = load_sprite("planet", planet_id)
            if sprite and sprite.image:
                print(f"    ✓ Successfully loaded {planet_id}")
                print(f"      Frame count: {sprite.frame_count}")
                print(f"      Sprite size: {sprite.sprite_size}")
            else:
                print(f"    ✗ Failed to load {planet_id}")
    
    print_separator()

def test_display_sprites():
    """Test displaying sprites using Pygame."""
    print("Testing sprite display with Pygame...")
    
    # Initialize Pygame
    pg.init()
    screen = pg.display.set_mode((800, 600))
    pg.display.set_caption("Sprite Test")
    
    # Load a test sprite
    test_ship = "scout"
    test_planet = "terrestrial_1"
    
    ship_sprite = load_sprite("ship", test_ship, "small")
    planet_sprite = load_sprite("planet", test_planet)
    
    if not (ship_sprite and ship_sprite.image) and not (planet_sprite and planet_sprite.image):
        print("No sprites could be loaded for display test.")
        pg.quit()
        return
    
    # Display loop
    running = True
    clock = pg.time.Clock()
    frame = 0
    max_frames = max(
        ship_sprite.frame_count if ship_sprite and ship_sprite.image else 0,
        planet_sprite.frame_count if planet_sprite and planet_sprite.image else 0,
        1
    )
    
    print(f"Starting display test with {max_frames} frames...")
    print("Close the window to continue.")
    
    while running:
        for event in pg.event.get():
            if event.type == pg.QUIT:
                running = False
        
        screen.fill((0, 0, 0))
        
        # Display ship sprite
        if ship_sprite and ship_sprite.image:
            ship_frame = ship_sprite.get_frame(frame % ship_sprite.frame_count)
            if ship_frame:
                screen.blit(ship_frame, (200, 300))
        
        # Display planet sprite
        if planet_sprite and planet_sprite.image:
            planet_frame = planet_sprite.get_frame(frame % planet_sprite.frame_count)
            if planet_frame:
                screen.blit(planet_frame, (500, 300))
        
        pg.display.flip()
        clock.tick(10)  # 10 FPS
        frame = (frame + 1) % max_frames
    
    pg.quit()
    print("Display test completed.")
    print_separator()

def main():
    """Run all sprite tests."""
    print("Current working directory:", os.getcwd())
    print_separator()
    
    # Initialize Pygame
    pg.init()
    
    # Run tests
    test_ship_sprites()
    test_planet_sprites()
    test_display_sprites()
    
    # Quit Pygame
    pg.quit()
    
    print("All tests completed.")

if __name__ == "__main__":
    main()
