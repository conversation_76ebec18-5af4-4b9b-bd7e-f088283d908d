#!/usr/bin/env python3
"""
Direct test of ship data loader without circular imports
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ship_data_loader():
    print("Testing ship data loader directly...")
    
    try:
        # Test the data loader directly
        from game_objects.ship_data_loader import SHIPS_REGISTRY, save_current_ships_to_file
        print("✓ Successfully imported ship_data_loader")
        
        # Test saving some sample ships to registry
        from game_objects.ship_data_loader import SimpleShip
        
        # Create a sample ship
        scout = SimpleShip(
            name="Scout",
            ship_class="fighter",
            size="small",
            outfit_space=20,
            cargo_space=5,
            turn_rate=1.75,
            acceleration=0.6,
            max_speed=4.0,
            shields=50,
            armor=30,
            min_tech_level=1,
            description="A fast, agile scout ship with minimal cargo space."
        )
        
        SHIPS_REGISTRY["scout"] = scout
        print(f"✓ Added scout to registry: {scout.name}")
        
        # Try to save to JSON
        if save_current_ships_to_file():
            print("✓ Successfully saved ships to JSON")
        else:
            print("✗ Failed to save ships to JSON")
            
        # Check if file was created
        if os.path.exists("ships_data.json"):
            print("✓ ships_data.json file created!")
            with open("ships_data.json", 'r') as f:
                import json
                data = json.load(f)
                print(f"✓ JSON contains {len(data)} ships")
                if "scout" in data:
                    print(f"✓ Scout data: {data['scout']['name']}")
            return True
        else:
            print("✗ ships_data.json file not found")
            return False
            
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ship_data_loader()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
