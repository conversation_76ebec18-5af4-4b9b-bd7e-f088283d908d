#!/usr/bin/env python3
"""
Test the full ship loading system
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_full_ship_system():
    print("Testing full ship loading system...")
    
    try:
        print("Step 1: Import ships module...")
        # This should trigger the ship loading logic
        from game_objects import ships
        
        print(f"Step 2: Check SHIPS dict - found {len(ships.SHIPS)} ships")
        
        # Test a few ships
        scout = ships.get_ship_by_id('scout')
        if scout:
            print(f"✓ Scout found: {scout.name} ({scout.ship_class})")
            print(f"  Max speed: {scout.max_speed}, Shields: {scout.shields}")
        else:
            print("✗ Scout not found")
            
        # List all ships
        print(f"\nAll ships:")
        for ship_id in list(ships.SHIPS.keys())[:5]:  # Just first 5
            ship = ships.SHIPS[ship_id]
            print(f"  {ship_id}: {ship.name}")
            
        # Check if JSON was created
        if os.path.exists("ships_data.json"):
            print(f"\n✓ ships_data.json was created!")
            with open("ships_data.json", 'r') as f:
                import json
                data = json.load(f)
                print(f"✓ Contains {len(data)} ships in JSON format")
        else:
            print(f"\n✗ ships_data.json was not created")
            
        return True
        
    except Exception as e:
        print(f"✗ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_ship_system()
    print(f"\nFinal Result: {'SUCCESS' if success else 'FAILED'}")
