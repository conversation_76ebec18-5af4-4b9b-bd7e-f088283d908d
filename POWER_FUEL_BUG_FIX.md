# 🔧 POWER/FUEL SYSTEM BUG FIX

## Issue Found
The error occurred because the game was using `SimpleShip` objects from the ship data loader, which didn't have the new power/fuel systems.

## Fix Applied
Updated `SimpleShip` class in `ship_data_loader.py` to include:

✅ **Power capacity calculation** based on ship size and class
✅ **Fuel capacity calculation** based on ship size and class  
✅ **Power regeneration rates** by ship size
✅ **Power/fuel consumption methods** (`consume_power`, `consume_fuel`)
✅ **Power regeneration method** (`regenerate_power`)

## Technical Details
The `SimpleShip` class now has identical power/fuel functionality to the main `Ship` class:

```python
# Power capacity: 60 (small) → 320 (capital dreadnoughts)
self.power_capacity = self._calculate_base_power()

# Fuel capacity: 80-100 (small) → 300-420 (capital)  
self.fuel_capacity = self._calculate_base_fuel()

# Power regen: 3/sec (small) → 6/sec (capital)
self.power_regen_rate = self._calculate_power_regen()
```

## Game Should Now Work
The power and fuel systems should now function correctly with ships loaded from JSON data files! 

**Test it by:**
1. Starting a new game
2. Watching power/fuel in the UI
3. Firing weapons to drain power
4. Using thrusters to consume power
5. Jumping between systems to use fuel

🎮 Ready to test the complete power/fuel system! ⚡⛽