    def auto_save_outfits(self):
        """Auto-save outfit changes to JSON file."""
        try:
            save_file = "outfits_data.json"
            outfit_data = {}

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                outfit_data[outfit_id] = {
                    'name': outfit.name,
                    'category': getattr(outfit, 'category', 'unknown'),
                    'subcategory': getattr(outfit, 'subcategory', ''),
                    'cost': getattr(outfit, 'cost', 0),
                    'space_required': getattr(outfit, 'space_required', 0),
                    'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                    'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                    'description': getattr(outfit, 'description', '')
                }

                # Add category-specific properties
                if outfit.category == "weapons":
                    outfit_data[outfit_id].update({
                        'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                        'damage': getattr(outfit, 'damage', 0),
                        'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                        'range': getattr(outfit, 'range', 0),
                        'energy_usage': getattr(outfit, 'energy_usage', 0),
                        'uses_ammo': getattr(outfit, 'uses_ammo', False),
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'max_ammo': getattr(outfit, 'max_ammo', 0)
                    })
                elif outfit.category == "ammunition":
                    outfit_data[outfit_id].update({
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'quantity': getattr(outfit, 'quantity', 0),
                        'damage': getattr(outfit, 'damage', 0),
                        'projectile_speed': getattr(outfit, 'projectile_speed', 0),
                        'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                        'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                        'explosion_radius': getattr(outfit, 'explosion_radius', 0)
                    })
                elif outfit.category == "defense":
                    outfit_data[outfit_id].update({
                        'shield_boost': getattr(outfit, 'shield_boost', 0),
                        'armor_boost': getattr(outfit, 'armor_boost', 0),
                        'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                        'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                    })

            with open(save_file, 'w') as f:
                json.dump(outfit_data, f, indent=2)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to auto-save outfits: {e}")

    def save_all_outfits(self):
        """Save all outfits to JSON file."""
        try:
            self.auto_save_outfits()
            messagebox.showinfo("Success", "All outfits saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save outfits: {e}")


def main():
    """Main entry point for the enhanced editor."""
    root = tk.Tk()
    editor = EnhancedEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
