"""
Quick test to see if the game runs without the faction_id error
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Testing if the game starts without faction_id error...")

try:
    print("1. Importing ships module...")
    from game_objects.ships import SHIPS, get_ship_by_id
    print(f"   ✓ Success! Found {len(SHIPS)} ships")
    
    print("2. Testing ship retrieval...")
    scout = get_ship_by_id('scout')
    if scout:
        print(f"   ✓ Scout found: {scout.name}")
    else:
        print("   ✗ Scout not found")
    
    print("3. Testing AI ship creation...")
    from game_objects.ai_ship import AIShip
    print("   ✓ AIShip class imported successfully")
    
    print("\n🎉 All basic tests passed!")
    print("The game should now run without the faction_id error.")
    
except Exception as e:
    print(f"✗ Error: {e}")
    import traceback
    traceback.print_exc()
