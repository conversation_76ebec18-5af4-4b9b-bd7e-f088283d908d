"""
Updated player.py to work with the new standardized outfit system
"""
import pygame as pg
import math
import random
import copy
from game_objects.ships import get_ship_by_id
from game_objects.standardized_outfits import *
from game_objects.example_outfits import *  # Load example outfits
from game_objects.projectiles import LaserProjectile, MissileProjectile
from game_objects.sprite_manager import load_sprite, get_angle_from_vector
from game_objects.outfit_rules_engine import update_all_outfit_systems

# --- Constants ---
DRAG_FACTOR = 0.002  # Reduced drag for more persistent momentum

# Color constants
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
WHITE = (255, 255, 255)

class Player(pg.sprite.Sprite):
    def __init__(self, game, player_name="Player", ship_name="Ship", ship_id="scout"):
        super().__init__()
        self.game = game
        self.player_name = player_name
        self.ship_name = ship_name

        # Get the ship from the ships module
        self.ship = get_ship_by_id(ship_id)
        if not self.ship:
            self.ship = get_ship_by_id("scout")

        # Try to load the ship's spritesheet
        print(f"Attempting to load spritesheet for ship: {ship_id} (size: {self.ship.size})")
        self.spritesheet = load_sprite("ship", ship_id, self.ship.size)

        # Set up the ship image
        if self.spritesheet and self.spritesheet.image:
            print(f"Using spritesheet for ship: {ship_id}")
            self.image_orig = self.spritesheet.get_frame(0)
            if not self.image_orig:
                print(f"Failed to get frame 0 from spritesheet for ship: {ship_id}")
                if self.ship.image:
                    print(f"Falling back to ship's static image")
                    self.image_orig = self.ship.image
                else:
                    print(f"Creating default image for ship: {ship_id}")
                    self.image_orig = pg.Surface([30, 20])
                    self.image_orig.fill((0, 255, 0))
        elif self.ship.image:
            print(f"No spritesheet found, using ship's static image for: {ship_id}")
            self.image_orig = self.ship.image
        else:
            print(f"No spritesheet or static image found, creating default for: {ship_id}")
            self.image_orig = pg.Surface([30, 20])
            self.image_orig.fill((0, 255, 0))

        self.image_orig.set_colorkey((0, 0, 0))
        self.image = self.image_orig.copy()
        self.rect = self.image.get_rect()

        # Ship stats from base ship
        self.base_max_shields = self.ship.max_shields
        self.base_shields = self.ship.shields
        self.base_max_armor = self.ship.max_armor
        self.base_armor = self.ship.armor
        self.base_acceleration = self.ship.acceleration
        self.base_turn_rate = self.ship.turn_rate
        self.base_max_speed = self.ship.max_speed

        # Current stats (modified by outfits)
        self.max_shields = self.base_max_shields
        self.shields = self.base_shields
        self.max_armor = self.base_max_armor
        self.armor = self.base_armor
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed
        
        # NEW: Power and Fuel Systems from ship
        self.power_capacity = self.ship.power_capacity
        self.power = self.power_capacity
        self.power_regen_rate = self.ship.power_regen_rate
        self.fuel_capacity = self.ship.fuel_capacity
        self.fuel = self.fuel_capacity

        # Outfit system
        self.cargo_space = self.ship.cargo_space
        self.outfit_space = self.ship.outfit_space
        self.used_outfit_space = 0
        self.installed_outfits = {}  # outfit_id -> quantity
        self.cargo = {}  # Dictionary to store cargo items and quantities

        # Fleet management
        self.fleet_ships = []

        # Player economy
        self.credits = 500000

        # Position
        self.pos = pg.math.Vector2(self.game.screen.get_width() // 2, self.game.screen.get_height() - 50)
        self.rect.center = self.pos

        # Velocity and angle
        self.vel = pg.math.Vector2(0, 0)
        self.angle = 0

        # Faction ID for projectile ownership
        self.faction_id = "player"

        # Shield recharge system
        self.shield_recharge_delay = 180
        self.shield_recharge_timer = 0

        # Weapon systems - NEW STANDARDIZED SYSTEM
        self.weapons = []  # List of equipped weapon outfits
        self.active_weapon_index = 0
        self.projectiles = pg.sprite.Group()
        self.last_fire_time = 0

        # Initialize with some default outfits
        self.install_outfit("laser_cannon")
        self.install_outfit("basic_shield")
        self.install_outfit("missile_rack")
        self.load_ammo("light_missile")  # Load some missiles

    def install_outfit(self, outfit_id):
        """Install an outfit on the ship."""
        outfit_template = get_outfit_by_id(outfit_id)
        if not outfit_template:
            self.game.set_status_message(f"Outfit '{outfit_id}' not found.", (255, 100, 100))
            return False

        # Check if outfit can be installed
        if not outfit_template.can_install_on_ship(self.ship):
            self.game.set_status_message(f"Cannot install {outfit_template.name} on this ship.", (255, 100, 100))
            return False

        # Check outfit space
        if self.used_outfit_space + outfit_template.space_required > self.outfit_space:
            self.game.set_status_message(f"Not enough outfit space for {outfit_template.name}.", (255, 100, 100))
            return False

        # Install the outfit
        if outfit_id in self.installed_outfits:
            self.installed_outfits[outfit_id] += 1
        else:
            self.installed_outfits[outfit_id] = 1

        self.used_outfit_space += outfit_template.space_required

        # Apply outfit effects
        self._recalculate_ship_stats()

        self.game.set_status_message(f"Installed {outfit_template.name}.", (100, 255, 100))
        return True

    def remove_outfit(self, outfit_id):
        """Remove an outfit from the ship."""
        if outfit_id not in self.installed_outfits or self.installed_outfits[outfit_id] <= 0:
            return False

        outfit_template = get_outfit_by_id(outfit_id)
        if not outfit_template:
            return False

        # Remove one instance
        self.installed_outfits[outfit_id] -= 1
        if self.installed_outfits[outfit_id] == 0:
            del self.installed_outfits[outfit_id]

        self.used_outfit_space -= outfit_template.space_required

        # Recalculate ship stats
        self._recalculate_ship_stats()
        return True

    def load_ammo(self, ammo_id):
        """Load ammunition into compatible weapons."""
        ammo_template = get_outfit_by_id(ammo_id)
        if not ammo_template or not isinstance(ammo_template, Ammunition):
            self.game.set_status_message(f"Ammunition '{ammo_id}' not found.", (255, 100, 100))
            return False

        loaded_count = 0
        for weapon in self.weapons:
            if weapon.uses_ammo and weapon.ammo_type == ammo_template.ammo_type:
                # Load ammo up to maximum
                ammo_to_load = min(ammo_template.quantity, weapon.max_ammo - weapon.current_ammo)
                weapon.current_ammo += ammo_to_load
                loaded_count += ammo_to_load

        if loaded_count > 0:
            self.game.set_status_message(f"Loaded {loaded_count}x {ammo_template.name}.", (100, 255, 100))
            return True
        else:
            self.game.set_status_message(f"No compatible launchers for {ammo_template.name}.", (255, 255, 100))
            return False

    def _has_reverse_thrusters(self):
        """Check if the player has reverse thrusters equipped."""
        # Check for specific reverse thruster outfits
        reverse_thruster_outfits = ["reverse_thrusters", "maneuvering_thrusters", "advanced_engines"]
        
        for outfit_id in reverse_thruster_outfits:
            if outfit_id in self.installed_outfits and self.installed_outfits[outfit_id] > 0:
                return True
        
        return False

    def _recalculate_ship_stats(self):
        """Recalculate ship stats based on installed outfits."""
        # Reset to base values
        self.max_shields = self.base_max_shields
        self.shields = min(self.shields, self.max_shields)  # Don't exceed new max
        self.max_armor = self.base_max_armor
        self.armor = min(self.armor, self.max_armor)
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed

        # Clear weapons list
        self.weapons = []

        # Apply effects from all installed outfits
        for outfit_id, quantity in self.installed_outfits.items():
            outfit_template = get_outfit_by_id(outfit_id)
            if not outfit_template:
                continue

            for _ in range(quantity):
                outfit_instance = outfit_template.clone()

                if isinstance(outfit_instance, Weapon):
                    self.weapons.append(outfit_instance)
                elif isinstance(outfit_instance, DefenseOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, EngineOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, ElectronicsOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, UtilityOutfit):
                    outfit_instance.apply_effects(self)

        # Ensure values don't go below minimum thresholds
        self.acceleration = max(0.05, self.acceleration)
        self.turn_rate = max(0.05, self.turn_rate)
        self.max_speed = max(1.0, self.max_speed)

    def fire_weapon(self, dt):
        """Fire the currently active weapon if possible."""
        if not self.weapons or self.active_weapon_index >= len(self.weapons):
            return None

        weapon = self.weapons[self.active_weapon_index]
        weapon.update(dt)

        if not weapon.can_fire():
            return None
        
        # NEW: Check power requirements
        power_cost = getattr(weapon, 'power_cost', self.ship.weapon_power_cost_base)
        if not self.ship.consume_power(power_cost):
            self.game.set_status_message("Insufficient power for weapon!", (255, 100, 100))
            return None

        # Get target for guided/turret weapons
        target = self.game.targeted_object

        # Check if we need a target
        if weapon.mount_type in [MOUNT_TYPE_TURRET, MOUNT_TYPE_GUIDED] and not target:
            self.game.set_status_message(f"No target selected for {weapon.name}.", (255, 255, 0))
            return None

        # Calculate spawn position and angle
        spawn_pos = self.pos.copy()
        projectile_angle = self.angle

        # Calculate forward direction
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        # Adjust spawn position and angle based on mount type
        if weapon.mount_type == MOUNT_TYPE_FIXED:
            offset_to_nose = self.image_orig.get_height() / 2
            spawn_pos = self.pos + forward * offset_to_nose
            projectile_angle = self.angle
        elif weapon.mount_type == MOUNT_TYPE_TURRET and target:
            to_target = target.pos - self.pos
            target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
            projectile_angle = target_angle % 360
            to_target.normalize()
            spawn_pos = self.pos + to_target * (self.rect.width / 3)
        elif weapon.mount_type == MOUNT_TYPE_GUIDED and target:
            spawn_pos = self.pos + forward * (self.rect.height / 2)
            projectile_angle = self.angle

        # Apply accuracy and spread
        if hasattr(weapon, 'accuracy') and weapon.accuracy < 1.0:
            accuracy_variation = (1.0 - weapon.accuracy) * 20
            projectile_angle += random.uniform(-accuracy_variation, accuracy_variation)

        if hasattr(weapon, 'spread') and weapon.spread > 0:
            projectile_angle += random.uniform(-weapon.spread, weapon.spread)

        # Create projectile based on weapon behavior
        if weapon.projectile_behavior == BEHAVIOR_INSTANT:
            # Laser-type weapons
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )
        elif weapon.projectile_behavior == BEHAVIOR_BEAM:
            # Beam weapons
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                beam=True
            )
        elif weapon.uses_ammo:
            # Missile/projectile weapons
            projectile = MissileProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                target,
                tracking=getattr(weapon, 'tracking_strength', 0.0)
            )
            # Consume ammo
            weapon.current_ammo -= 1
        else:
            # Default projectile
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )

        # Fire the weapon
        if weapon.fire():
            self.game.all_sprites.add(projectile)
            self.projectiles.add(projectile)
            return projectile

        return None

    def cycle_weapons(self):
        """Cycle to the next available weapon."""
        if not self.weapons:
            return

        self.active_weapon_index = (self.active_weapon_index + 1) % len(self.weapons)
        weapon = self.weapons[self.active_weapon_index]
        self.game.set_status_message(f"Selected: {weapon.name}", (0, 255, 0))

    def get_active_weapon_info(self):
        """Get information about the currently active weapon."""
        if not self.weapons or self.active_weapon_index >= len(self.weapons):
            return "No weapons equipped"

        weapon = self.weapons[self.active_weapon_index]

        if weapon.uses_ammo:
            return f"{weapon.name} ({weapon.current_ammo}/{weapon.max_ammo})"
        else:
            return weapon.name

    def update(self, dt=1/60):
        """Update player state."""
        # Prepare input state for rules engine
        keystate = pg.key.get_pressed()
        input_state = {
            'thrust_forward': keystate[pg.K_w],
            'thrust_reverse': keystate[pg.K_s],
            'turn_left': keystate[pg.K_a],
            'turn_right': keystate[pg.K_d],
            'afterburner': keystate.get(pg.K_LSHIFT, False),  # Optional afterburner key
            'fire_weapons': keystate[pg.K_SPACE]
        }
        
        # Get nearby objects for rules engine
        nearby_objects = []
        if hasattr(self.game, 'all_sprites'):
            for sprite in self.game.all_sprites:
                if sprite != self:
                    nearby_objects.append(sprite)
        
        # Apply outfit rules engine - this handles defense, engine, and electronics systems
        try:
            update_all_outfit_systems(self, dt, input_state, nearby_objects)
        except Exception as e:
            print(f"Error in outfit rules engine: {e}")
        
        acc = pg.math.Vector2(0, 0)

        # Handle rotation
        if keystate[pg.K_d]:
            # NEW: Check power for turning
            turn_power_cost = 1.0 * dt  # Small power cost for turning
            if self.ship.consume_power(turn_power_cost):
                self.angle += self.turn_rate
                if self.angle >= 360:
                    self.angle -= 360
            elif self.ship.power > 0:  # Minimal turning with remaining power
                self.angle += self.turn_rate * 0.1
                if self.angle >= 360:
                    self.angle -= 360
            # No turning at all if power is 0
            
        if keystate[pg.K_a]:
            # NEW: Check power for turning
            turn_power_cost = 1.0 * dt  # Small power cost for turning
            if self.ship.consume_power(turn_power_cost):
                self.angle -= self.turn_rate
                if self.angle < 0:
                    self.angle += 360
            elif self.ship.power > 0:  # Minimal turning with remaining power
                self.angle -= self.turn_rate * 0.1
                if self.angle < 0:
                    self.angle += 360
            # No turning at all if power is 0

        # Update ship image
        self.rotate()

        # Calculate forward direction
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        # Apply thrust - IMPROVED PHYSICS-BASED SYSTEM
        thrust_strength = self.acceleration
        thrust_power_used = False
        
        # Check if player has reverse thrusters outfit installed
        has_reverse_thrusters = self._has_reverse_thrusters()
        
        # NEW: Complete power blackout system
        if self.ship.power <= 0:
            # COMPLETE BLACKOUT - no movement at all
            acc = pg.math.Vector2(0, 0)
            if random.random() < 0.05:  # Occasional blackout message
                self.game.set_status_message("POWER FAILURE - All systems offline!", RED)
        else:
            # Normal power operations - FORWARD THRUST ONLY (unless reverse thrusters equipped)
            if keystate[pg.K_w]:
                # Check power for forward thrust
                power_cost = self.ship.thruster_power_cost * dt
                if self.ship.consume_power(power_cost):
                    acc = forward * thrust_strength
                    thrust_power_used = True
                else:
                    # Very reduced thrust when low on power
                    acc = forward * thrust_strength * 0.1
                    if random.random() < 0.1:
                        self.game.set_status_message("Critical power - minimal thrust!", RED)
            
            # S key behavior depends on whether reverse thrusters are equipped
            if keystate[pg.K_s]:
                if has_reverse_thrusters:
                    # Equipped with reverse thrusters - direct reverse thrust
                    power_cost = self.ship.thruster_power_cost * 0.6 * dt  # Slightly less efficient
                    if self.ship.consume_power(power_cost):
                        acc = -forward * thrust_strength * 0.6  # 60% reverse power
                        thrust_power_used = True
                        if random.random() < 0.02:  # Occasional message
                            self.game.set_status_message("Reverse thrusters engaged", GREEN)
                    else:
                        # Very minimal reverse thrust when low on power
                        acc = -forward * thrust_strength * 0.05
                else:
                    # No reverse thrusters - show helpful message occasionally
                    if random.random() < 0.02:  # Occasional reminder
                        self.game.set_status_message("No reverse thrusters - turn around to slow down!", YELLOW)

        # Check for weapon firing
        if keystate[pg.K_SPACE]:
            # NEW: Weapons completely disabled without power
            if self.ship.power <= 0:
                if random.random() < 0.1:  # Occasional message
                    self.game.set_status_message("No power for weapons!", RED)
            else:
                self.fire_weapon(dt)

        # Apply acceleration
        self.vel += acc

        # Apply drag
        if self.vel.length_squared() > 0:
            drag = self.vel.normalize() * DRAG_FACTOR * self.vel.length_squared()
            if drag.length_squared() < self.vel.length_squared():
                self.vel -= drag
            else:
                self.vel = pg.math.Vector2(0, 0)

        # Limit speed
        if self.vel.length_squared() > self.max_speed * self.max_speed:
            self.vel.scale_to_length(self.max_speed)

        # Update position
        self.pos += self.vel
        self.rect.center = self.pos

        # World boundary checks
        ship_width = self.rect.width
        ship_height = self.rect.height

        if self.pos.x > self.game.camera.width - ship_width / 2:
            self.pos.x = self.game.camera.width - ship_width / 2
            self.vel.x *= -0.5
        if self.pos.x < ship_width / 2:
            self.pos.x = ship_width / 2
            self.vel.x *= -0.5
        if self.pos.y > self.game.camera.height - ship_height / 2:
            self.pos.y = self.game.camera.height - ship_height / 2
            self.vel.y *= -0.5
        if self.pos.y < ship_height / 2:
            self.pos.y = ship_height / 2
            self.vel.y *= -0.5
        self.rect.center = self.pos

        # Update all weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Shield regeneration
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            # NEW: Check power for shield regeneration
            shield_power_cost = self.ship.shield_regen_power_cost * dt
            if self.ship.consume_power(shield_power_cost):
                self.shields = min(self.max_shields, self.shields + 0.1)
            # If no power, shields don't regenerate
        
        # NEW: Power regeneration
        self.ship.regenerate_power(dt)
        
        # Update current power/fuel from ship (in case modified by outfits)
        self.power = self.ship.power
        self.fuel = self.ship.fuel

    def rotate(self):
        """Update the ship's image based on its current angle."""
        old_center = self.rect.center

        try:
            if self.spritesheet and self.spritesheet.image:
                frame = self.spritesheet.get_frame_by_angle(self.angle)
                if frame:
                    self.image = frame
                else:
                    self.image = pg.transform.rotate(self.image_orig, -self.angle)
            else:
                self.image = pg.transform.rotate(self.image_orig, -self.angle)
        except Exception as e:
            print(f"Error during rotation: {e}")
            self.image = self.image_orig.copy()

        self.rect = self.image.get_rect()
        self.rect.center = old_center

    def take_damage(self, amount, attacker_faction_id=None):
        """Take damage from an attack."""
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.armor += self.shields
                self.shields = 0
        else:
            self.armor -= amount

        # Reset shield recharge timer
        self.shield_recharge_timer = self.shield_recharge_delay

        # Check if player is destroyed
        if self.armor <= 0:
            self.game.set_status_message("Your ship has been destroyed!", (255, 0, 0))
            print("Player ship destroyed!")
            self.game.playing = False
            self.game.state = "GAME_OVER"
