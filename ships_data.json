{"scout": {"id": "scout", "name": "Scout", "ship_class": "fighter", "size": "small", "cost": 15000, "outfit_space": 20, "cargo_space": 5, "mass": 1.0, "min_tech_level": 1, "max_speed": 4.0, "acceleration": 0.6, "turn_rate": 1.75, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\small\\scout_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "A fast, agile scout ship with minimal cargo space.", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "light_fighter": {"id": "light_fighter", "name": "Light Fighter", "ship_class": "fighter", "size": "small", "cost": 18000, "outfit_space": 25, "cargo_space": 3, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.75, "acceleration": 0.5, "turn_rate": 1.5, "shields": 60, "armor": 40, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\small\\light_fighter_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "A nimble fighter designed for dogfighting.", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "freighter": {"id": "freighter", "name": "Freighter", "ship_class": "freighter", "size": "medium", "cost": 45000, "outfit_space": 35, "cargo_space": 120, "mass": 1.0, "min_tech_level": 2, "max_speed": 2.25, "acceleration": 0.25, "turn_rate": 0.5, "shields": 80, "armor": 100, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\small\\light_fighter_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "A standard cargo ship with good capacity.", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "kraken": {"id": "kraken", "name": "<PERSON><PERSON><PERSON>", "ship_class": "fighter", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "corvette": {"id": "corvette", "name": "Corvette", "ship_class": "corvette", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\large\\corvette_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "heavyfighter": {"id": "heavyfighter", "name": "Heavyfighter", "ship_class": "fighter", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\medium\\heavy_fighter_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "gunship": {"id": "gunship", "name": "Gunship", "ship_class": "corvette", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\medium\\gunship_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "passengerliner": {"id": "passengerliner", "name": "Passengerliner", "ship_class": "transport", "size": "medium", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\medium\\passenger_liner_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "bulkcarrier": {"id": "bulkcarrier", "name": "Bulkcarrier", "ship_class": "transport", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\large\\bulk_carrier_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "frigate": {"id": "frigate", "name": "Frigate", "ship_class": "frigate", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\large\\frigate_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "heavyfreighter": {"id": "heavyfreighter", "name": "<PERSON>freighter", "ship_class": "freighter", "size": "large", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\large\\heavy_freighter_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "battleship": {"id": "battleship", "name": "Battleship", "ship_class": "battleship", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\capital\\battleship_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "carrier": {"id": "carrier", "name": "Carrier", "ship_class": "carrier", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\capital\\carrier_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "cruiser": {"id": "cruiser", "name": "Cruiser", "ship_class": "cruiser", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\capital\\cruiser_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "destroyer": {"id": "destroyer", "name": "Destroyer", "ship_class": "destroyer", "size": "capital", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\capital\\destroyer_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}, "courier": {"id": "courier", "name": "Courier", "ship_class": "freighter", "size": "small", "cost": 10000, "outfit_space": 20, "cargo_space": 10, "mass": 1.0, "min_tech_level": 1, "max_speed": 3.0, "acceleration": 0.5, "turn_rate": 1.0, "shields": 50, "armor": 30, "shield_recharge_rate": 1.0, "shipyard_sprite": "", "game_sprite": "assets\\images\\sprites\\ships\\small\\courier_spritesheet.png", "animation_frames": 1, "sprite_size": 32, "animation_type": "rotation", "frame_rate": 30, "idle_sequence": "0-0", "thrust_sequence": "0-0", "weapon_hardpoints": [], "turret_hardpoints": [], "engine_hardpoints": [], "fuel_capacity": 100, "energy_capacity": 100, "energy_recharge": 10.0, "crew_capacity": 1, "passenger_capacity": 0, "required_reputation": 0, "required_missions": [], "availability_systems": [], "maneuverability_class": "medium", "engine_efficiency": 1.0, "hyperspace_speed": 1.0, "special_abilities": [], "ship_roles": ["general"], "description": "", "manufacturer": "Unknown", "origin": "Unknown", "default_outfits": {}}}