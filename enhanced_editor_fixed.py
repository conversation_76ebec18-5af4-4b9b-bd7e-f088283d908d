"""
Escape Velocity Py - Enhanced Content Editor - CORRECTED VERSION
Properly reflects the actual game outfit system implementation
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import os
import sys
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

try:
    from game_objects.standardized_outfits import *
    from game_objects.example_outfits import *
    from game_objects.ships import *
    from game_objects.standardized_ships import *
    print("Successfully imported outfit and ship systems")
    print(f"Found {len(OUTFITS_REGISTRY)} outfits in registry")
    print(f"Found {len(SHIPS)} ships in registry")
    print(f"Found {len(STANDARDIZED_SHIPS_REGISTRY)} standardized ships in registry")
except ImportError as e:
    print(f"Error importing game systems: {e}")
    print("Make sure the game files are in the correct location")
    # Create empty registries to prevent crashes
    OUTFITS_REGISTRY = {}
    SHIPS = {}
    STANDARDIZED_SHIPS_REGISTRY = {}

class EnhancedEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("EV Py - Enhanced Content Editor - CORRECTED")
        self.root.geometry("1400x900")

        # Current selections
        self.current_outfit = None
        self.current_ship = None

        # Define behavior constants in case imports fail
        self.behavior_types = ["instant", "dumbfire", "guided", "beam", "delayed", "proximity"]
        self.mount_types = ["fixed", "turret"]
        self.damage_types = ["energy", "kinetic", "explosive"]
        self.ammo_types = ["light_missile", "heavy_missile", "torpedo", "rocket"]
        self.ship_sizes = ["small", "medium", "large", "capital"]

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Setup comprehensive UI with all tabs."""
        # Create notebook for categories
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create all tabs
        self.create_weapons_tab()
        self.create_ammunition_tab()
        self.create_defense_tab()
        self.create_engines_tab()
        self.create_electronics_tab()
        self.create_utility_tab()
        self.create_ships_tab()

    def load_data(self):
        """Load all outfit and ship data."""
        print("Loading outfit and ship data...")
        self.load_saved_outfits()  # Try to load saved data first
        self.load_outfits()
        self.load_ships()

    def load_saved_outfits(self):
        """Load previously saved outfit data if available."""
        save_file = "outfits_data.json"
        if os.path.exists(save_file):
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing outfits with saved data
                for outfit_id, outfit_data in saved_data.items():
                    if outfit_id in OUTFITS_REGISTRY:
                        outfit = OUTFITS_REGISTRY[outfit_id]
                        # Update basic properties
                        for prop in ['name', 'cost', 'space_required', 'min_tech_level', 'outfitter_icon', 'description']:
                            if prop in outfit_data and hasattr(outfit, prop):
                                setattr(outfit, prop, outfit_data[prop])

                        # Update category-specific properties
                        if outfit.category == "weapons":
                            for prop in ['mount_type', 'damage', 'fire_rate', 'range', 'energy_usage', 'uses_ammo', 'ammo_type', 'max_ammo']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])
                        elif outfit.category == "ammunition":
                            for prop in ['ammo_type', 'quantity', 'damage', 'projectile_speed', 'projectile_behavior', 'tracking_strength', 'explosion_radius']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])
                        elif outfit.category == "defense":
                            for prop in ['shield_boost', 'armor_boost', 'shield_recharge_boost', 'damage_reduction']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])

                print(f"Loaded saved outfit data from {save_file}")

            except Exception as e:
                print(f"Failed to load saved outfit data: {e}")

    def load_outfits(self):
        """Load outfit data."""
        print(f"Found {len(OUTFITS_REGISTRY)} outfits in registry")
        self.load_weapons()
        self.load_ammunition()
        self.load_defense()
        self.load_engines()
        self.load_electronics()
        self.load_utility()

    def load_ships(self):
        """Load ship data."""
        print(f"Found {len(SHIPS)} ships in registry")
        if hasattr(self, 'ship_listbox'):
            self.ship_listbox.delete(0, tk.END)
            for ship_id, ship in SHIPS.items():
                display_name = f"{ship.name} ({ship.size})"
                self.ship_listbox.insert(tk.END, display_name)

    def load_weapons(self):
        """Load weapons into the listbox."""
        if hasattr(self, 'weapon_listbox'):
            self.weapon_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                # Check if it's a weapon by category or by class
                if (hasattr(outfit, 'category') and outfit.category == "weapons") or \
                   (hasattr(outfit, '__class__') and 'Weapon' in str(outfit.__class__)):
                    subcategory = getattr(outfit, 'subcategory', 'unknown')
                    display_name = f"{outfit.name} ({subcategory})"
                    self.weapon_listbox.insert(tk.END, display_name)

    def create_weapons_tab(self):
        """Create weapons tab with list."""
        weapons_frame = ttk.Frame(self.notebook)
        self.notebook.add(weapons_frame, text="Weapons")

        # Create horizontal layout
        paned = ttk.PanedWindow(weapons_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Weapon list
        left_frame = ttk.LabelFrame(paned, text="Weapons Library")
        paned.add(left_frame, weight=1)

        # Weapon listbox
        self.weapon_listbox = tk.Listbox(left_frame)
        self.weapon_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.weapon_listbox.bind("<<ListboxSelect>>", self.on_weapon_select)

        # Buttons for weapon management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="New Weapon", command=self.create_new_weapon).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_weapon).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", command=self.export_weapons).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Save All", command=self.save_all_outfits).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Weapon Editor")
        paned.add(right_frame, weight=2)

        self.setup_weapon_editor(right_frame)

    def setup_weapon_editor(self, parent):
        """Setup the weapon editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.weapon_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.weapon_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.weapon_cost_var, width=10).grid(row=0, column=3, padx=5)

        # Space and Mount Type
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.weapon_space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=50, textvariable=self.weapon_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Mount Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_mount_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.weapon_mount_var, values=self.mount_types, width=15).grid(row=0, column=3, padx=5)

        # Tech Level and Image
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row3, text="Tech Level:").grid(row=0, column=0, sticky=tk.W)
        self.weapon_tech_level_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=10, textvariable=self.weapon_tech_level_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row3, text="Image Path:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.weapon_image_var, width=25).grid(row=0, column=3, padx=5)
        ttk.Button(row3, text="Browse", command=self.browse_weapon_image).grid(row=0, column=4, padx=2)

        # Combat Properties
        combat_frame = ttk.LabelFrame(parent, text="Combat Properties")
        combat_frame.pack(fill=tk.X, padx=5, pady=5)

        combat_grid = ttk.Frame(combat_frame)
        combat_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(combat_grid, text="Damage:").grid(row=0, column=0, sticky=tk.W)
        self.weapon_damage_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=1, to=1000, textvariable=self.weapon_damage_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(combat_grid, text="Fire Rate:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_fire_rate_var = tk.DoubleVar()
        ttk.Spinbox(combat_grid, from_=0.1, to=20.0, increment=0.1, textvariable=self.weapon_fire_rate_var, width=10).grid(row=0, column=3, padx=5)

        # Range and Energy
        ttk.Label(combat_grid, text="Range:").grid(row=1, column=0, sticky=tk.W)
        self.weapon_range_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=50, to=2000, textvariable=self.weapon_range_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(combat_grid, text="Energy Usage:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_energy_var = tk.IntVar()
        ttk.Spinbox(combat_grid, from_=0, to=200, textvariable=self.weapon_energy_var, width=10).grid(row=1, column=3, padx=5)

        # Launcher Properties (for weapons that use ammo)
        self.launcher_frame = ttk.LabelFrame(parent, text="Launcher Properties (for ammo-using weapons)")

        launcher_grid = ttk.Frame(self.launcher_frame)
        launcher_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(launcher_grid, text="Uses Ammo:").grid(row=0, column=0, sticky=tk.W)
        self.weapon_uses_ammo_var = tk.BooleanVar()
        ttk.Checkbutton(launcher_grid, variable=self.weapon_uses_ammo_var,
                       command=self.on_ammo_toggle).grid(row=0, column=1, padx=5, sticky=tk.W)

        ttk.Label(launcher_grid, text="Ammo Type:").grid(row=1, column=0, sticky=tk.W)
        self.weapon_ammo_type_var = tk.StringVar()
        ttk.Combobox(launcher_grid, textvariable=self.weapon_ammo_type_var,
                    values=self.ammo_types).grid(row=1, column=1, padx=5)

        ttk.Label(launcher_grid, text="Max Ammo:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.weapon_max_ammo_var = tk.IntVar()
        ttk.Spinbox(launcher_grid, from_=1, to=100, textvariable=self.weapon_max_ammo_var, width=10).grid(row=1, column=3, padx=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Weapon", command=self.save_weapon).pack(side=tk.RIGHT, padx=5)

    def on_weapon_select(self, event=None):
        """Handle weapon selection from list."""
        selection = self.weapon_listbox.curselection()
        if not selection:
            return

        # Get selected weapon
        weapons = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "weapons") or \
               (hasattr(outfit, '__class__') and 'Weapon' in str(outfit.__class__)):
                weapons.append(outfit)

        if selection[0] < len(weapons):
            weapon = weapons[selection[0]]
            self.load_weapon_into_editor(weapon)

    def load_weapon_into_editor(self, weapon):
        """Load weapon data into the editor."""
        self.current_outfit = weapon

        # Load basic properties
        self.weapon_name_var.set(weapon.name)
        self.weapon_cost_var.set(getattr(weapon, 'cost', 1000))
        self.weapon_space_var.set(getattr(weapon, 'space_required', 1))
        self.weapon_mount_var.set(getattr(weapon, 'mount_type', 'fixed'))

        # Load tech level and image
        self.weapon_tech_level_var.set(getattr(weapon, 'min_tech_level', 1))
        self.weapon_image_var.set(getattr(weapon, 'outfitter_icon', ''))

        # Load combat properties
        self.weapon_damage_var.set(getattr(weapon, 'damage', 10))
        self.weapon_fire_rate_var.set(getattr(weapon, 'fire_rate', 1.0))
        self.weapon_range_var.set(getattr(weapon, 'range', 300))
        self.weapon_energy_var.set(getattr(weapon, 'energy_usage', 5))

        # Load launcher properties
        self.weapon_uses_ammo_var.set(getattr(weapon, 'uses_ammo', False))
        self.weapon_ammo_type_var.set(getattr(weapon, 'ammo_type', ''))
        self.weapon_max_ammo_var.set(getattr(weapon, 'max_ammo', 0))

        # Update UI visibility
        self.on_ammo_toggle()

    def browse_weapon_image(self):
        """Browse for weapon image file."""
        filename = filedialog.askopenfilename(
            title="Select Weapon Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            # Convert to relative path if possible
            try:
                relative_path = os.path.relpath(filename, os.getcwd())
                self.weapon_image_var.set(relative_path)
            except ValueError:
                # If relative path fails, use absolute path
                self.weapon_image_var.set(filename)

    def on_ammo_toggle(self):
        """Show/hide launcher frame based on uses_ammo checkbox."""
        if self.weapon_uses_ammo_var.get():
            self.launcher_frame.pack(fill=tk.X, padx=5, pady=5)
        else:
            self.launcher_frame.pack_forget()

    def save_weapon(self):
        """Save the current weapon with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No weapon selected to save")
            return

        try:
            # Update basic properties
            self.current_outfit.name = self.weapon_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.weapon_cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.weapon_space_var.get()
            if hasattr(self.current_outfit, 'mount_type'):
                self.current_outfit.mount_type = self.weapon_mount_var.get()
            if hasattr(self.current_outfit, 'min_tech_level'):
                self.current_outfit.min_tech_level = self.weapon_tech_level_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.weapon_image_var.get()

            # Update combat properties
            if hasattr(self.current_outfit, 'damage'):
                self.current_outfit.damage = self.weapon_damage_var.get()
            if hasattr(self.current_outfit, 'fire_rate'):
                self.current_outfit.fire_rate = self.weapon_fire_rate_var.get()
            if hasattr(self.current_outfit, 'range'):
                self.current_outfit.range = self.weapon_range_var.get()
            if hasattr(self.current_outfit, 'energy_usage'):
                self.current_outfit.energy_usage = self.weapon_energy_var.get()

            # Update launcher properties
            if hasattr(self.current_outfit, 'uses_ammo'):
                self.current_outfit.uses_ammo = self.weapon_uses_ammo_var.get()
            if hasattr(self.current_outfit, 'ammo_type'):
                self.current_outfit.ammo_type = self.weapon_ammo_type_var.get()
            if hasattr(self.current_outfit, 'max_ammo'):
                self.current_outfit.max_ammo = self.weapon_max_ammo_var.get()

            messagebox.showinfo("Success", f"Saved weapon: {self.current_outfit.name}")
            self.load_weapons()  # Refresh the list
            self.auto_save_outfits()  # Auto-save changes

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save weapon: {e}")

    def create_new_weapon(self):
        """Create a new weapon."""
        weapon_id = simpledialog.askstring("New Weapon", "Enter a unique ID for the new weapon:")
        if not weapon_id:
            return

        if weapon_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"A weapon with ID '{weapon_id}' already exists")
            return

        # Create a basic weapon object
        try:
            # Try to use the standardized system if available
            if 'Weapon' in globals():
                new_weapon = Weapon(weapon_id, weapon_id.replace('_', ' ').title(), "energy")
            else:
                # Create a simple object if classes aren't available
                class SimpleWeapon:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "weapons"
                        self.subcategory = "energy"
                        self.cost = 1000
                        self.space_required = 1
                        self.mount_type = "fixed"
                        self.damage = 10
                        self.fire_rate = 1.0
                        self.range = 300
                        self.energy_usage = 5
                        self.uses_ammo = False
                        self.ammo_type = ""
                        self.max_ammo = 0

                new_weapon = SimpleWeapon(weapon_id, weapon_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[weapon_id] = new_weapon
            self.load_weapons()
            messagebox.showinfo("Success", f"Created new weapon: {new_weapon.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create weapon: {e}")

    def delete_weapon(self):
        """Delete the selected weapon."""
        selection = self.weapon_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No weapon selected to delete")
            return

        # Get selected weapon
        weapons = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "weapons") or \
               (hasattr(outfit, '__class__') and 'Weapon' in str(outfit.__class__)):
                weapons.append((outfit_id, outfit))

        if selection[0] < len(weapons):
            weapon_id, weapon = weapons[selection[0]]

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{weapon.name}'?")
            if result:
                del OUTFITS_REGISTRY[weapon_id]
                self.load_weapons()
                messagebox.showinfo("Success", f"Deleted weapon: {weapon.name}")

    def export_weapons(self):
        """Export weapons to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Weapons",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                weapons_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    if (hasattr(outfit, 'category') and outfit.category == "weapons") or \
                       (hasattr(outfit, '__class__') and 'Weapon' in str(outfit.__class__)):
                        weapons_data[outfit_id] = {
                            'name': outfit.name,
                            'category': getattr(outfit, 'category', 'weapons'),
                            'subcategory': getattr(outfit, 'subcategory', 'energy'),
                            'cost': getattr(outfit, 'cost', 1000),
                            'space_required': getattr(outfit, 'space_required', 1),
                            'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                            'damage': getattr(outfit, 'damage', 10),
                            'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                            'range': getattr(outfit, 'range', 300),
                            'energy_usage': getattr(outfit, 'energy_usage', 5),
                            'uses_ammo': getattr(outfit, 'uses_ammo', False),
                            'ammo_type': getattr(outfit, 'ammo_type', ''),
                            'max_ammo': getattr(outfit, 'max_ammo', 0)
                        }

                with open(filename, 'w') as f:
                    json.dump(weapons_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(weapons_data)} weapons to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export weapons: {e}")

    # =========================================================================
    # AMMUNITION TAB - Projectile behavior is defined HERE, not on launchers!
    # =========================================================================

    def create_ammunition_tab(self):
        """Create ammunition tab."""
        ammo_frame = ttk.Frame(self.notebook)
        self.notebook.add(ammo_frame, text="Ammunition")

        # Create horizontal layout
        paned = ttk.PanedWindow(ammo_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Ammunition list
        left_frame = ttk.LabelFrame(paned, text="Ammunition Library")
        paned.add(left_frame, weight=1)

        # Ammunition listbox
        self.ammo_listbox = tk.Listbox(left_frame)
        self.ammo_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.ammo_listbox.bind("<<ListboxSelect>>", self.on_ammo_select)

        # Buttons for ammunition management
        ammo_button_frame = ttk.Frame(left_frame)
        ammo_button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(ammo_button_frame, text="New Ammo", command=self.create_new_ammo).pack(side=tk.LEFT, padx=2)
        ttk.Button(ammo_button_frame, text="Delete", command=self.delete_ammo).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Ammunition Editor")
        paned.add(right_frame, weight=2)

        self.setup_ammo_editor(right_frame)

    def load_ammunition(self):
        """Load ammunition into the listbox."""
        if hasattr(self, 'ammo_listbox'):
            self.ammo_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                if (hasattr(outfit, 'category') and outfit.category == "ammunition") or \
                   (hasattr(outfit, '__class__') and 'Ammunition' in str(outfit.__class__)):
                    ammo_type = getattr(outfit, 'ammo_type', 'unknown')
                    display_name = f"{outfit.name} ({ammo_type})"
                    self.ammo_listbox.insert(tk.END, display_name)

    def setup_ammo_editor(self, parent):
        """Setup the ammunition editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # Name, Type, Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.ammo_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.ammo_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ammo_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=10000, textvariable=self.ammo_cost_var, width=10).grid(row=0, column=3, padx=5)

        # Ammo Type and Quantity
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Ammo Type:").grid(row=0, column=0, sticky=tk.W)
        self.ammo_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.ammo_type_var, values=self.ammo_types, width=15).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Quantity:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ammo_quantity_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=100, textvariable=self.ammo_quantity_var, width=10).grid(row=0, column=3, padx=5)

        # Projectile Properties (THIS IS WHERE BEHAVIOR IS DEFINED!)
        projectile_frame = ttk.LabelFrame(parent, text="Projectile Properties")
        projectile_frame.pack(fill=tk.X, padx=5, pady=5)

        proj_grid = ttk.Frame(projectile_frame)
        proj_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(proj_grid, text="Damage:").grid(row=0, column=0, sticky=tk.W)
        self.ammo_damage_var = tk.IntVar()
        ttk.Spinbox(proj_grid, from_=1, to=1000, textvariable=self.ammo_damage_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(proj_grid, text="Speed:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ammo_speed_var = tk.IntVar()
        ttk.Spinbox(proj_grid, from_=1, to=50, textvariable=self.ammo_speed_var, width=10).grid(row=0, column=3, padx=5)

        # Behavior and Tracking
        ttk.Label(proj_grid, text="Behavior:").grid(row=1, column=0, sticky=tk.W)
        self.ammo_behavior_var = tk.StringVar()
        ttk.Combobox(proj_grid, textvariable=self.ammo_behavior_var, values=self.behavior_types, width=15).grid(row=1, column=1, padx=5)

        ttk.Label(proj_grid, text="Tracking (0.0-1.0):").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.ammo_tracking_var = tk.DoubleVar()
        ttk.Spinbox(proj_grid, from_=0.0, to=1.0, increment=0.1, textvariable=self.ammo_tracking_var, width=10).grid(row=1, column=3, padx=5)

        # Explosion Properties
        explosion_frame = ttk.LabelFrame(parent, text="Explosion Properties")
        explosion_frame.pack(fill=tk.X, padx=5, pady=5)

        exp_grid = ttk.Frame(explosion_frame)
        exp_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(exp_grid, text="Explosion Radius:").grid(row=0, column=0, sticky=tk.W)
        self.ammo_explosion_var = tk.IntVar()
        ttk.Spinbox(exp_grid, from_=0, to=100, textvariable=self.ammo_explosion_var, width=10).grid(row=0, column=1, padx=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Ammunition", command=self.save_ammo).pack(side=tk.RIGHT, padx=5)

    def on_ammo_select(self, event=None):
        """Handle ammunition selection from list."""
        selection = self.ammo_listbox.curselection()
        if not selection:
            return

        # Get selected ammunition
        ammo_list = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "ammunition") or \
               (hasattr(outfit, '__class__') and 'Ammunition' in str(outfit.__class__)):
                ammo_list.append(outfit)

        if selection[0] < len(ammo_list):
            ammo = ammo_list[selection[0]]
            self.load_ammo_into_editor(ammo)

    def load_ammo_into_editor(self, ammo):
        """Load ammunition data into the editor."""
        self.current_outfit = ammo

        # Load basic properties
        self.ammo_name_var.set(ammo.name)
        self.ammo_cost_var.set(getattr(ammo, 'cost', 500))
        self.ammo_type_var.set(getattr(ammo, 'ammo_type', ''))
        self.ammo_quantity_var.set(getattr(ammo, 'quantity', 10))

        # Load projectile properties
        self.ammo_damage_var.set(getattr(ammo, 'damage', 50))
        self.ammo_speed_var.set(getattr(ammo, 'projectile_speed', 8))
        self.ammo_behavior_var.set(getattr(ammo, 'projectile_behavior', 'dumbfire'))
        self.ammo_tracking_var.set(getattr(ammo, 'tracking_strength', 0.0))
        self.ammo_explosion_var.set(getattr(ammo, 'explosion_radius', 20))

    def save_ammo(self):
        """Save the current ammunition with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No ammunition selected to save")
            return

        try:
            # Update basic properties
            self.current_outfit.name = self.ammo_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.ammo_cost_var.get()
            if hasattr(self.current_outfit, 'ammo_type'):
                self.current_outfit.ammo_type = self.ammo_type_var.get()
            if hasattr(self.current_outfit, 'quantity'):
                self.current_outfit.quantity = self.ammo_quantity_var.get()

            # Update projectile properties
            if hasattr(self.current_outfit, 'damage'):
                self.current_outfit.damage = self.ammo_damage_var.get()
            if hasattr(self.current_outfit, 'projectile_speed'):
                self.current_outfit.projectile_speed = self.ammo_speed_var.get()
            if hasattr(self.current_outfit, 'projectile_behavior'):
                self.current_outfit.projectile_behavior = self.ammo_behavior_var.get()
            if hasattr(self.current_outfit, 'tracking_strength'):
                self.current_outfit.tracking_strength = self.ammo_tracking_var.get()
            if hasattr(self.current_outfit, 'explosion_radius'):
                self.current_outfit.explosion_radius = self.ammo_explosion_var.get()

            messagebox.showinfo("Success", f"Saved ammunition: {self.current_outfit.name}")
            self.load_ammunition()  # Refresh the list
            self.auto_save_outfits()  # Auto-save changes

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ammunition: {e}")

    def create_new_ammo(self):
        """Create a new ammunition."""
        ammo_id = simpledialog.askstring("New Ammunition", "Enter a unique ID for the new ammunition:")
        if not ammo_id:
            return

        if ammo_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"Ammunition with ID '{ammo_id}' already exists")
            return

        try:
            # Create a basic ammunition object
            if 'Ammunition' in globals():
                new_ammo = Ammunition(ammo_id, ammo_id.replace('_', ' ').title(), "light_missile")
            else:
                # Create a simple object if classes aren't available
                class SimpleAmmo:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "ammunition"
                        self.ammo_type = "light_missile"
                        self.cost = 500
                        self.quantity = 10
                        self.damage = 50
                        self.projectile_speed = 8
                        self.projectile_behavior = "dumbfire"
                        self.tracking_strength = 0.0
                        self.explosion_radius = 20

                new_ammo = SimpleAmmo(ammo_id, ammo_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[ammo_id] = new_ammo
            self.load_ammunition()
            messagebox.showinfo("Success", f"Created new ammunition: {new_ammo.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create ammunition: {e}")

    def delete_ammo(self):
        """Delete the selected ammunition."""
        selection = self.ammo_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No ammunition selected to delete")
            return

        # Get selected ammunition
        ammo_list = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "ammunition") or \
               (hasattr(outfit, '__class__') and 'Ammunition' in str(outfit.__class__)):
                ammo_list.append((outfit_id, outfit))

        if selection[0] < len(ammo_list):
            ammo_id, ammo = ammo_list[selection[0]]

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{ammo.name}'?")
            if result:
                del OUTFITS_REGISTRY[ammo_id]
                self.load_ammunition()
                messagebox.showinfo("Success", f"Deleted ammunition: {ammo.name}")

    # =========================================================================
    # DEFENSE TAB - Now fully implemented
    # =========================================================================

    def create_defense_tab(self):
        """Create comprehensive defense tab with all new rules engine features."""
        defense_frame = ttk.Frame(self.notebook)
        self.notebook.add(defense_frame, text="Defense")

        # Create horizontal layout
        paned = ttk.PanedWindow(defense_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Defense list
        left_frame = ttk.LabelFrame(paned, text="Defense Library")
        paned.add(left_frame, weight=1)

        # Defense listbox
        self.defense_listbox = tk.Listbox(left_frame)
        self.defense_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.defense_listbox.bind("<<ListboxSelect>>", self.on_defense_select)

        # Buttons for defense management
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="New Defense", command=self.create_new_defense).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_defense).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Export", command=self.export_defense).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Defense Editor")
        paned.add(right_frame, weight=2)

        self.setup_defense_editor(right_frame)

    def setup_defense_editor(self, parent):
        """Setup the comprehensive defense editor interface with rules engine properties."""
        # Create scrollable frame for all defense properties
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # Name and Cost
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.defense_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.defense_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.defense_cost_var, width=10).grid(row=0, column=3, padx=5)

        # Space, Type, and Tech Level
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.defense_space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.defense_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.defense_type_var, 
                    values=["shields", "armor", "point_defense", "ecm", "reactive"], width=15).grid(row=0, column=3, padx=5)

        # Tech Level and Image
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row3, text="Tech Level:").grid(row=0, column=0, sticky=tk.W)
        self.defense_tech_level_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=10, textvariable=self.defense_tech_level_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row3, text="Image Path:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_image_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.defense_image_var, width=25).grid(row=0, column=3, padx=5)
        ttk.Button(row3, text="Browse", command=self.browse_defense_image).grid(row=0, column=4, padx=2)

        # Mount and Targeting Properties (for active defenses)
        mount_frame = ttk.LabelFrame(scrollable_frame, text="Mount & Targeting Properties")
        mount_frame.pack(fill=tk.X, padx=5, pady=5)

        mount_grid = ttk.Frame(mount_frame)
        mount_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(mount_grid, text="Mount Type:").grid(row=0, column=0, sticky=tk.W)
        self.defense_mount_type_var = tk.StringVar()
        ttk.Combobox(mount_grid, textvariable=self.defense_mount_type_var, 
                    values=["passive", "fixed", "turret", "omni"], width=15).grid(row=0, column=1, padx=5)

        ttk.Label(mount_grid, text="Target Types:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_target_types_var = tk.StringVar()
        ttk.Entry(mount_grid, textvariable=self.defense_target_types_var, width=25).grid(row=0, column=3, padx=5)
        ttk.Label(mount_grid, text="(comma-separated)").grid(row=0, column=4, padx=2)

        # Passive Defense Properties
        passive_frame = ttk.LabelFrame(scrollable_frame, text="Passive Defense Properties")
        passive_frame.pack(fill=tk.X, padx=5, pady=5)

        passive_grid = ttk.Frame(passive_frame)
        passive_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(passive_grid, text="Shield Boost:").grid(row=0, column=0, sticky=tk.W)
        self.defense_shield_boost_var = tk.IntVar()
        ttk.Spinbox(passive_grid, from_=0, to=1000, textvariable=self.defense_shield_boost_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(passive_grid, text="Armor Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_armor_boost_var = tk.IntVar()
        ttk.Spinbox(passive_grid, from_=0, to=1000, textvariable=self.defense_armor_boost_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(passive_grid, text="Shield Recharge:").grid(row=1, column=0, sticky=tk.W)
        self.defense_shield_recharge_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.defense_shield_recharge_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(passive_grid, text="Damage Reduction:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_damage_reduction_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=0.8, increment=0.01, textvariable=self.defense_damage_reduction_var, width=10).grid(row=1, column=3, padx=5)

        ttk.Label(passive_grid, text="Armor Repair Rate:").grid(row=2, column=0, sticky=tk.W)
        self.defense_armor_repair_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=10.0, increment=0.1, textvariable=self.defense_armor_repair_var, width=10).grid(row=2, column=1, padx=5)

        ttk.Label(passive_grid, text="Energy Drain (per sec):").grid(row=2, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_energy_drain_var = tk.DoubleVar()
        ttk.Spinbox(passive_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.defense_energy_drain_var, width=10).grid(row=2, column=3, padx=5)

        # Active Defense Properties (Point Defense)
        active_frame = ttk.LabelFrame(scrollable_frame, text="Active Defense Properties (Point Defense)")
        active_frame.pack(fill=tk.X, padx=5, pady=5)

        active_grid = ttk.Frame(active_frame)
        active_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(active_grid, text="Defense Range:").grid(row=0, column=0, sticky=tk.W)
        self.defense_range_var = tk.IntVar()
        ttk.Spinbox(active_grid, from_=50, to=1000, textvariable=self.defense_range_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(active_grid, text="Fire Rate (shots/sec):").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_fire_rate_var = tk.DoubleVar()
        ttk.Spinbox(active_grid, from_=0.1, to=20.0, increment=0.1, textvariable=self.defense_fire_rate_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(active_grid, text="Accuracy (0.0-1.0):").grid(row=1, column=0, sticky=tk.W)
        self.defense_accuracy_var = tk.DoubleVar()
        ttk.Spinbox(active_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.defense_accuracy_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(active_grid, text="Power Cost (per shot):").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_power_cost_var = tk.DoubleVar()
        ttk.Spinbox(active_grid, from_=1.0, to=50.0, increment=0.1, textvariable=self.defense_power_cost_var, width=10).grid(row=1, column=3, padx=5)

        ttk.Label(active_grid, text="Firing Arc (degrees):").grid(row=2, column=0, sticky=tk.W)
        self.defense_firing_arc_var = tk.IntVar()
        ttk.Spinbox(active_grid, from_=10, to=360, textvariable=self.defense_firing_arc_var, width=10).grid(row=2, column=1, padx=5)

        # Electronic Warfare Properties
        ecm_frame = ttk.LabelFrame(scrollable_frame, text="Electronic Warfare Properties")
        ecm_frame.pack(fill=tk.X, padx=5, pady=5)

        ecm_grid = ttk.Frame(ecm_frame)
        ecm_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(ecm_grid, text="Jam Strength (0.0-1.0):").grid(row=0, column=0, sticky=tk.W)
        self.defense_jam_strength_var = tk.DoubleVar()
        ttk.Spinbox(ecm_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.defense_jam_strength_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(ecm_grid, text="Jam Resistance (0.0-1.0):").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_jam_resistance_var = tk.DoubleVar()
        ttk.Spinbox(ecm_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.defense_jam_resistance_var, width=10).grid(row=0, column=3, padx=5)

        # Reactive Defense Properties
        reactive_frame = ttk.LabelFrame(scrollable_frame, text="Reactive Defense Properties")
        reactive_frame.pack(fill=tk.X, padx=5, pady=5)

        reactive_grid = ttk.Frame(reactive_frame)
        reactive_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(reactive_grid, text="Activation Chance (0.0-1.0):").grid(row=0, column=0, sticky=tk.W)
        self.defense_activation_chance_var = tk.DoubleVar()
        ttk.Spinbox(reactive_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.defense_activation_chance_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(reactive_grid, text="Activation Threshold:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.defense_activation_threshold_var = tk.IntVar()
        ttk.Spinbox(reactive_grid, from_=0, to=100, textvariable=self.defense_activation_threshold_var, width=10).grid(row=0, column=3, padx=5)

        # Description
        description_frame = ttk.LabelFrame(scrollable_frame, text="Description")
        description_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(description_frame, text="Description:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.defense_description_text = tk.Text(description_frame, height=3, wrap=tk.WORD)
        self.defense_description_text.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Defense", command=self.save_defense).pack(side=tk.RIGHT, padx=5)

    def browse_defense_image(self):
        """Browse for defense outfit image file."""
        filename = filedialog.askopenfilename(
            title="Select Defense Outfit Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            try:
                relative_path = os.path.relpath(filename, os.getcwd())
                self.defense_image_var.set(relative_path)
            except ValueError:
                self.defense_image_var.set(filename)

    def load_defense(self):
        """Load defense outfits."""
        if hasattr(self, 'defense_listbox'):
            self.defense_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                if (hasattr(outfit, 'category') and outfit.category == "defense") or \
                   (hasattr(outfit, '__class__') and 'Defense' in str(outfit.__class__)):
                    subcategory = getattr(outfit, 'subcategory', 'unknown')
                    display_name = f"{outfit.name} ({subcategory})"
                    self.defense_listbox.insert(tk.END, display_name)

    def on_defense_select(self, event=None):
        """Handle defense selection from list."""
        selection = self.defense_listbox.curselection()
        if not selection:
            return

        # Get selected defense
        defense_list = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "defense") or \
               (hasattr(outfit, '__class__') and 'Defense' in str(outfit.__class__)):
                defense_list.append(outfit)

        if selection[0] < len(defense_list):
            defense = defense_list[selection[0]]
            self.load_defense_into_editor(defense)

    def load_defense_into_editor(self, defense):
        """Load defense data into the editor with all new properties."""
        self.current_outfit = defense

        # Load basic properties
        self.defense_name_var.set(defense.name)
        self.defense_cost_var.set(getattr(defense, 'cost', 1000))
        self.defense_space_var.set(getattr(defense, 'space_required', 1))
        self.defense_type_var.set(getattr(defense, 'subcategory', 'shields'))
        self.defense_tech_level_var.set(getattr(defense, 'min_tech_level', 1))
        self.defense_image_var.set(getattr(defense, 'outfitter_icon', ''))

        # Load mount and targeting properties
        self.defense_mount_type_var.set(getattr(defense, 'mount_type', 'passive'))
        target_types = getattr(defense, 'target_types', ['projectiles'])
        self.defense_target_types_var.set(', '.join(target_types))

        # Load passive defense properties
        self.defense_shield_boost_var.set(getattr(defense, 'shield_boost', 0))
        self.defense_armor_boost_var.set(getattr(defense, 'armor_boost', 0))
        self.defense_shield_recharge_var.set(getattr(defense, 'shield_recharge_boost', 0.0))
        self.defense_damage_reduction_var.set(getattr(defense, 'damage_reduction', 0.0))
        self.defense_armor_repair_var.set(getattr(defense, 'armor_repair_rate', 0.0))
        self.defense_energy_drain_var.set(getattr(defense, 'energy_drain', 0.0))

        # Load active defense properties
        self.defense_range_var.set(getattr(defense, 'defense_range', 200))
        self.defense_fire_rate_var.set(getattr(defense, 'fire_rate', 2.0))
        self.defense_accuracy_var.set(getattr(defense, 'accuracy', 0.7))
        self.defense_power_cost_var.set(getattr(defense, 'power_cost', 5.0))
        self.defense_firing_arc_var.set(getattr(defense, 'firing_arc', 45))

        # Load electronic warfare properties
        self.defense_jam_strength_var.set(getattr(defense, 'jam_strength', 0.0))
        self.defense_jam_resistance_var.set(getattr(defense, 'jam_resistance', 0.0))

        # Load reactive defense properties
        self.defense_activation_chance_var.set(getattr(defense, 'activation_chance', 0.0))
        self.defense_activation_threshold_var.set(getattr(defense, 'activation_threshold', 0))

        # Load description
        self.defense_description_text.delete(1.0, tk.END)
        self.defense_description_text.insert(1.0, getattr(defense, 'description', ''))

    def save_defense(self):
        """Save the current defense with all editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No defense outfit selected to save")
            return

        try:
            # Update basic properties
            self.current_outfit.name = self.defense_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.defense_cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.defense_space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.defense_type_var.get()
            if hasattr(self.current_outfit, 'min_tech_level'):
                self.current_outfit.min_tech_level = self.defense_tech_level_var.get()
            if hasattr(self.current_outfit, 'outfitter_icon'):
                self.current_outfit.outfitter_icon = self.defense_image_var.get()

            # Update mount and targeting properties
            if hasattr(self.current_outfit, 'mount_type'):
                self.current_outfit.mount_type = self.defense_mount_type_var.get()
            else:
                self.current_outfit.mount_type = self.defense_mount_type_var.get()
            
            # Parse target types
            target_types_text = self.defense_target_types_var.get().strip()
            if target_types_text:
                target_types = [t.strip() for t in target_types_text.split(',') if t.strip()]
                if hasattr(self.current_outfit, 'target_types'):
                    self.current_outfit.target_types = target_types
                else:
                    self.current_outfit.target_types = target_types

            # Update passive defense properties
            if hasattr(self.current_outfit, 'shield_boost'):
                self.current_outfit.shield_boost = self.defense_shield_boost_var.get()
            if hasattr(self.current_outfit, 'armor_boost'):
                self.current_outfit.armor_boost = self.defense_armor_boost_var.get()
            if hasattr(self.current_outfit, 'shield_recharge_boost'):
                self.current_outfit.shield_recharge_boost = self.defense_shield_recharge_var.get()
            if hasattr(self.current_outfit, 'damage_reduction'):
                self.current_outfit.damage_reduction = self.defense_damage_reduction_var.get()
            if hasattr(self.current_outfit, 'armor_repair_rate'):
                self.current_outfit.armor_repair_rate = self.defense_armor_repair_var.get()
            else:
                self.current_outfit.armor_repair_rate = self.defense_armor_repair_var.get()
            if hasattr(self.current_outfit, 'energy_drain'):
                self.current_outfit.energy_drain = self.defense_energy_drain_var.get()

            # Update active defense properties
            if hasattr(self.current_outfit, 'defense_range'):
                self.current_outfit.defense_range = self.defense_range_var.get()
            else:
                self.current_outfit.defense_range = self.defense_range_var.get()
            
            if hasattr(self.current_outfit, 'fire_rate'):
                self.current_outfit.fire_rate = self.defense_fire_rate_var.get()
            else:
                self.current_outfit.fire_rate = self.defense_fire_rate_var.get()
                
            if hasattr(self.current_outfit, 'accuracy'):
                self.current_outfit.accuracy = self.defense_accuracy_var.get()
            else:
                self.current_outfit.accuracy = self.defense_accuracy_var.get()
                
            if hasattr(self.current_outfit, 'power_cost'):
                self.current_outfit.power_cost = self.defense_power_cost_var.get()
            else:
                self.current_outfit.power_cost = self.defense_power_cost_var.get()
                
            if hasattr(self.current_outfit, 'firing_arc'):
                self.current_outfit.firing_arc = self.defense_firing_arc_var.get()
            else:
                self.current_outfit.firing_arc = self.defense_firing_arc_var.get()

            # Update electronic warfare properties
            if hasattr(self.current_outfit, 'jam_strength'):
                self.current_outfit.jam_strength = self.defense_jam_strength_var.get()
            else:
                self.current_outfit.jam_strength = self.defense_jam_strength_var.get()
                
            if hasattr(self.current_outfit, 'jam_resistance'):
                self.current_outfit.jam_resistance = self.defense_jam_resistance_var.get()
            else:
                self.current_outfit.jam_resistance = self.defense_jam_resistance_var.get()

            # Update reactive defense properties
            if hasattr(self.current_outfit, 'activation_chance'):
                self.current_outfit.activation_chance = self.defense_activation_chance_var.get()
            else:
                self.current_outfit.activation_chance = self.defense_activation_chance_var.get()
                
            if hasattr(self.current_outfit, 'activation_threshold'):
                self.current_outfit.activation_threshold = self.defense_activation_threshold_var.get()
            else:
                self.current_outfit.activation_threshold = self.defense_activation_threshold_var.get()

            # Update description
            if hasattr(self.current_outfit, 'description'):
                self.current_outfit.description = self.defense_description_text.get(1.0, tk.END).strip()
            else:
                self.current_outfit.description = self.defense_description_text.get(1.0, tk.END).strip()

            messagebox.showinfo("Success", f"Saved defense outfit: {self.current_outfit.name}")
            self.load_defense()  # Refresh the list
            self.auto_save_outfits()  # Auto-save changes

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save defense outfit: {e}")

    def export_defense(self):
        """Export defense outfits to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Defense Outfits",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                defense_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    if (hasattr(outfit, 'category') and outfit.category == "defense") or \
                       (hasattr(outfit, '__class__') and 'Defense' in str(outfit.__class__)):
                        defense_data[outfit_id] = {
                            'name': outfit.name,
                            'category': getattr(outfit, 'category', 'defense'),
                            'subcategory': getattr(outfit, 'subcategory', 'shields'),
                            'cost': getattr(outfit, 'cost', 1000),
                            'space_required': getattr(outfit, 'space_required', 1),
                            'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                            'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                            'mount_type': getattr(outfit, 'mount_type', 'passive'),
                            'target_types': getattr(outfit, 'target_types', ['projectiles']),
                            'shield_boost': getattr(outfit, 'shield_boost', 0),
                            'armor_boost': getattr(outfit, 'armor_boost', 0),
                            'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                            'damage_reduction': getattr(outfit, 'damage_reduction', 0.0),
                            'armor_repair_rate': getattr(outfit, 'armor_repair_rate', 0.0),
                            'energy_drain': getattr(outfit, 'energy_drain', 0.0),
                            'defense_range': getattr(outfit, 'defense_range', 200),
                            'fire_rate': getattr(outfit, 'fire_rate', 2.0),
                            'accuracy': getattr(outfit, 'accuracy', 0.7),
                            'power_cost': getattr(outfit, 'power_cost', 5.0),
                            'firing_arc': getattr(outfit, 'firing_arc', 45),
                            'jam_strength': getattr(outfit, 'jam_strength', 0.0),
                            'jam_resistance': getattr(outfit, 'jam_resistance', 0.0),
                            'activation_chance': getattr(outfit, 'activation_chance', 0.0),
                            'activation_threshold': getattr(outfit, 'activation_threshold', 0),
                            'description': getattr(outfit, 'description', '')
                        }

                with open(filename, 'w') as f:
                    json.dump(defense_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(defense_data)} defense outfits to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export defense outfits: {e}")

    def create_new_defense(self):
        """Create a new defense outfit."""
        defense_id = simpledialog.askstring("New Defense", "Enter a unique ID for the new defense outfit:")
        if not defense_id:
            return

        if defense_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"A defense outfit with ID '{defense_id}' already exists")
            return

        try:
            # Create a basic defense object
            if 'DefenseOutfit' in globals():
                new_defense = DefenseOutfit(defense_id, defense_id.replace('_', ' ').title(), "shields")
            else:
                # Create a simple object if classes aren't available
                class SimpleDefense:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "defense"
                        self.subcategory = "shields"
                        self.cost = 1000
                        self.space_required = 1
                        self.shield_boost = 50
                        self.armor_boost = 0
                        self.shield_recharge_boost = 0.0
                        self.damage_reduction = 0.0

                new_defense = SimpleDefense(defense_id, defense_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[defense_id] = new_defense
            self.load_defense()
            messagebox.showinfo("Success", f"Created new defense outfit: {new_defense.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create defense outfit: {e}")

    def delete_defense(self):
        """Delete the selected defense outfit."""
        selection = self.defense_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No defense outfit selected to delete")
            return

        # Get selected defense
        defense_list = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "defense") or \
               (hasattr(outfit, '__class__') and 'Defense' in str(outfit.__class__)):
                defense_list.append((outfit_id, outfit))

        if selection[0] < len(defense_list):
            defense_id, defense = defense_list[selection[0]]

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{defense.name}'?")
            if result:
                del OUTFITS_REGISTRY[defense_id]
                self.load_defense()
                messagebox.showinfo("Success", f"Deleted defense outfit: {defense.name}")

    def create_engines_tab(self):
        """Create comprehensive engines tab."""
        engines_frame = ttk.Frame(self.notebook)
        self.notebook.add(engines_frame, text="Engines")

        # Create horizontal layout
        paned = ttk.PanedWindow(engines_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Engine list
        left_frame = ttk.LabelFrame(paned, text="Engine Library")
        paned.add(left_frame, weight=1)

        # Engine listbox
        self.engine_listbox = tk.Listbox(left_frame)
        self.engine_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.engine_listbox.bind("<<ListboxSelect>>", self.on_engine_select)

        # Buttons for engine management
        engine_button_frame = ttk.Frame(left_frame)
        engine_button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(engine_button_frame, text="New Engine", command=self.create_new_engine).pack(side=tk.LEFT, padx=2)
        ttk.Button(engine_button_frame, text="Delete", command=self.delete_engine).pack(side=tk.LEFT, padx=2)
        ttk.Button(engine_button_frame, text="Export", command=self.export_engines).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Engine Editor")
        paned.add(right_frame, weight=2)

        self.setup_engine_editor(right_frame)

    def load_engines(self):
        """Load engine outfits."""
        if hasattr(self, 'engine_listbox'):
            self.engine_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                if (hasattr(outfit, 'category') and outfit.category == "engines") or \
                   (hasattr(outfit, '__class__') and 'Engine' in str(outfit.__class__)):
                    subcategory = getattr(outfit, 'subcategory', 'unknown')
                    display_name = f"{outfit.name} ({subcategory})"
                    self.engine_listbox.insert(tk.END, display_name)

    def setup_engine_editor(self, parent):
        """Setup the engine editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.engine_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.engine_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.engine_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.engine_cost_var, width=10).grid(row=0, column=3, padx=5)

        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.engine_space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.engine_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.engine_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.engine_type_var, values=["thruster", "steering", "afterburner"], width=15).grid(row=0, column=3, padx=5)

        # Performance Properties
        performance_frame = ttk.LabelFrame(scrollable_frame, text="Performance Properties")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)

        perf_grid = ttk.Frame(performance_frame)
        perf_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(perf_grid, text="Acceleration Boost:").grid(row=0, column=0, sticky=tk.W)
        self.engine_accel_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.0, to=5.0, increment=0.1, textvariable=self.engine_accel_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(perf_grid, text="Max Speed Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.engine_speed_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.0, to=10.0, increment=0.1, textvariable=self.engine_speed_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(perf_grid, text="Turn Rate Boost:").grid(row=1, column=0, sticky=tk.W)
        self.engine_turn_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.0, to=3.0, increment=0.1, textvariable=self.engine_turn_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(perf_grid, text="Energy Drain:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.engine_energy_drain_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.engine_energy_drain_var, width=10).grid(row=1, column=3, padx=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Engine", command=self.save_engine).pack(side=tk.RIGHT, padx=5)

    def on_engine_select(self, event=None):
        """Handle engine selection from list."""
        selection = self.engine_listbox.curselection()
        if not selection:
            return

        engines = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "engines") or \
               (hasattr(outfit, '__class__') and 'Engine' in str(outfit.__class__)):
                engines.append(outfit)

        if selection[0] < len(engines):
            engine = engines[selection[0]]
            self.load_engine_into_editor(engine)

    def load_engine_into_editor(self, engine):
        """Load engine data into the editor."""
        self.current_outfit = engine

        self.engine_name_var.set(engine.name)
        self.engine_cost_var.set(getattr(engine, 'cost', 1000))
        self.engine_space_var.set(getattr(engine, 'space_required', 1))
        self.engine_type_var.set(getattr(engine, 'subcategory', 'thruster'))
        self.engine_accel_var.set(getattr(engine, 'acceleration_boost', 0.0))
        self.engine_speed_var.set(getattr(engine, 'max_speed_boost', 0.0))
        self.engine_turn_var.set(getattr(engine, 'turn_rate_boost', 0.0))
        self.engine_energy_drain_var.set(getattr(engine, 'energy_drain', 0.0))

    def save_engine(self):
        """Save the current engine with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No engine selected to save")
            return

        try:
            self.current_outfit.name = self.engine_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.engine_cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.engine_space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.engine_type_var.get()
            if hasattr(self.current_outfit, 'acceleration_boost'):
                self.current_outfit.acceleration_boost = self.engine_accel_var.get()
            if hasattr(self.current_outfit, 'max_speed_boost'):
                self.current_outfit.max_speed_boost = self.engine_speed_var.get()
            if hasattr(self.current_outfit, 'turn_rate_boost'):
                self.current_outfit.turn_rate_boost = self.engine_turn_var.get()
            if hasattr(self.current_outfit, 'energy_drain'):
                self.current_outfit.energy_drain = self.engine_energy_drain_var.get()

            messagebox.showinfo("Success", f"Saved engine: {self.current_outfit.name}")
            self.load_engines()
            self.auto_save_outfits()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save engine: {e}")

    def create_new_engine(self):
        """Create a new engine."""
        engine_id = simpledialog.askstring("New Engine", "Enter a unique ID for the new engine:")
        if not engine_id:
            return

        if engine_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"An engine with ID '{engine_id}' already exists")
            return

        try:
            if 'EngineOutfit' in globals():
                new_engine = EngineOutfit(engine_id, engine_id.replace('_', ' ').title(), "thruster")
            else:
                class SimpleEngine:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "engines"
                        self.subcategory = "thruster"
                        self.cost = 1000
                        self.space_required = 1
                        self.acceleration_boost = 0.0
                        self.max_speed_boost = 0.0
                        self.turn_rate_boost = 0.0
                        self.energy_drain = 0.0

                new_engine = SimpleEngine(engine_id, engine_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[engine_id] = new_engine
            self.load_engines()
            messagebox.showinfo("Success", f"Created new engine: {new_engine.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create engine: {e}")

    def delete_engine(self):
        """Delete the selected engine."""
        selection = self.engine_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No engine selected to delete")
            return

        engines = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "engines") or \
               (hasattr(outfit, '__class__') and 'Engine' in str(outfit.__class__)):
                engines.append((outfit_id, outfit))

        if selection[0] < len(engines):
            engine_id, engine = engines[selection[0]]
            result = messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{engine.name}'?")
            if result:
                del OUTFITS_REGISTRY[engine_id]
                self.load_engines()
                messagebox.showinfo("Success", f"Deleted engine: {engine.name}")

    def export_engines(self):
        """Export engines to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Engines",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                engines_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    if (hasattr(outfit, 'category') and outfit.category == "engines") or \
                       (hasattr(outfit, '__class__') and 'Engine' in str(outfit.__class__)):
                        engines_data[outfit_id] = {
                            'name': outfit.name,
                            'category': getattr(outfit, 'category', 'engines'),
                            'subcategory': getattr(outfit, 'subcategory', 'thruster'),
                            'cost': getattr(outfit, 'cost', 1000),
                            'space_required': getattr(outfit, 'space_required', 1),
                            'acceleration_boost': getattr(outfit, 'acceleration_boost', 0.0),
                            'max_speed_boost': getattr(outfit, 'max_speed_boost', 0.0),
                            'turn_rate_boost': getattr(outfit, 'turn_rate_boost', 0.0),
                            'energy_drain': getattr(outfit, 'energy_drain', 0.0)
                        }

                with open(filename, 'w') as f:
                    json.dump(engines_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(engines_data)} engines to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export engines: {e}")

    def create_electronics_tab(self):
        """Create comprehensive electronics tab."""
        electronics_frame = ttk.Frame(self.notebook)
        self.notebook.add(electronics_frame, text="Electronics")

        # Create horizontal layout
        paned = ttk.PanedWindow(electronics_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Electronics list
        left_frame = ttk.LabelFrame(paned, text="Electronics Library")
        paned.add(left_frame, weight=1)

        # Electronics listbox
        self.electronics_listbox = tk.Listbox(left_frame)
        self.electronics_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.electronics_listbox.bind("<<ListboxSelect>>", self.on_electronics_select)

        # Buttons for electronics management
        electronics_button_frame = ttk.Frame(left_frame)
        electronics_button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(electronics_button_frame, text="New Electronics", command=self.create_new_electronics).pack(side=tk.LEFT, padx=2)
        ttk.Button(electronics_button_frame, text="Delete", command=self.delete_electronics).pack(side=tk.LEFT, padx=2)
        ttk.Button(electronics_button_frame, text="Export", command=self.export_electronics).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Electronics Editor")
        paned.add(right_frame, weight=2)

        self.setup_electronics_editor(right_frame)

    def load_electronics(self):
        """Load electronics outfits."""
        if hasattr(self, 'electronics_listbox'):
            self.electronics_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                if (hasattr(outfit, 'category') and outfit.category == "electronics") or \
                   (hasattr(outfit, '__class__') and 'Electronics' in str(outfit.__class__)):
                    subcategory = getattr(outfit, 'subcategory', 'unknown')
                    display_name = f"{outfit.name} ({subcategory})"
                    self.electronics_listbox.insert(tk.END, display_name)

    def setup_electronics_editor(self, parent):
        """Setup the electronics editor interface."""
        # Create scrollable frame
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.elec_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.elec_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.elec_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.elec_cost_var, width=10).grid(row=0, column=3, padx=5)

        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.elec_space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.elec_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.elec_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.elec_type_var, values=["sensors", "targeting", "jamming", "communication", "scanning"], width=15).grid(row=0, column=3, padx=5)

        # Sensor Properties
        sensor_frame = ttk.LabelFrame(scrollable_frame, text="Sensor Properties")
        sensor_frame.pack(fill=tk.X, padx=5, pady=5)

        sensor_grid = ttk.Frame(sensor_frame)
        sensor_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(sensor_grid, text="Sensor Range Boost:").grid(row=0, column=0, sticky=tk.W)
        self.elec_sensor_range_var = tk.DoubleVar()
        ttk.Spinbox(sensor_grid, from_=0.0, to=2000.0, increment=10.0, textvariable=self.elec_sensor_range_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(sensor_grid, text="Targeting Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.elec_targeting_var = tk.DoubleVar()
        ttk.Spinbox(sensor_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.elec_targeting_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(sensor_grid, text="Jamming Strength:").grid(row=1, column=0, sticky=tk.W)
        self.elec_jamming_var = tk.DoubleVar()
        ttk.Spinbox(sensor_grid, from_=0.0, to=1.0, increment=0.01, textvariable=self.elec_jamming_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(sensor_grid, text="Energy Drain:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.elec_energy_drain_var = tk.DoubleVar()
        ttk.Spinbox(sensor_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.elec_energy_drain_var, width=10).grid(row=1, column=3, padx=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Electronics", command=self.save_electronics).pack(side=tk.RIGHT, padx=5)

    def on_electronics_select(self, event=None):
        """Handle electronics selection from list."""
        selection = self.electronics_listbox.curselection()
        if not selection:
            return

        electronics = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "electronics") or \
               (hasattr(outfit, '__class__') and 'Electronics' in str(outfit.__class__)):
                electronics.append(outfit)

        if selection[0] < len(electronics):
            electronic = electronics[selection[0]]
            self.load_electronics_into_editor(electronic)

    def load_electronics_into_editor(self, electronic):
        """Load electronics data into the editor."""
        self.current_outfit = electronic

        self.elec_name_var.set(electronic.name)
        self.elec_cost_var.set(getattr(electronic, 'cost', 1000))
        self.elec_space_var.set(getattr(electronic, 'space_required', 1))
        self.elec_type_var.set(getattr(electronic, 'subcategory', 'sensors'))
        self.elec_sensor_range_var.set(getattr(electronic, 'sensor_range_boost', 0.0))
        self.elec_targeting_var.set(getattr(electronic, 'targeting_boost', 0.0))
        self.elec_jamming_var.set(getattr(electronic, 'jamming_strength', 0.0))
        self.elec_energy_drain_var.set(getattr(electronic, 'energy_drain', 0.0))

    def save_electronics(self):
        """Save the current electronics with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No electronics selected to save")
            return

        try:
            self.current_outfit.name = self.elec_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.elec_cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.elec_space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.elec_type_var.get()
            if hasattr(self.current_outfit, 'sensor_range_boost'):
                self.current_outfit.sensor_range_boost = self.elec_sensor_range_var.get()
            if hasattr(self.current_outfit, 'targeting_boost'):
                self.current_outfit.targeting_boost = self.elec_targeting_var.get()
            if hasattr(self.current_outfit, 'jamming_strength'):
                self.current_outfit.jamming_strength = self.elec_jamming_var.get()
            if hasattr(self.current_outfit, 'energy_drain'):
                self.current_outfit.energy_drain = self.elec_energy_drain_var.get()

            messagebox.showinfo("Success", f"Saved electronics: {self.current_outfit.name}")
            self.load_electronics()
            self.auto_save_outfits()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save electronics: {e}")

    def create_new_electronics(self):
        """Create a new electronics outfit."""
        electronics_id = simpledialog.askstring("New Electronics", "Enter a unique ID for the new electronics:")
        if not electronics_id:
            return

        if electronics_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"Electronics with ID '{electronics_id}' already exists")
            return

        try:
            if 'ElectronicsOutfit' in globals():
                new_electronics = ElectronicsOutfit(electronics_id, electronics_id.replace('_', ' ').title(), "sensors")
            else:
                class SimpleElectronics:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "electronics"
                        self.subcategory = "sensors"
                        self.cost = 1000
                        self.space_required = 1
                        self.sensor_range_boost = 0.0
                        self.targeting_boost = 0.0
                        self.jamming_strength = 0.0
                        self.energy_drain = 0.0

                new_electronics = SimpleElectronics(electronics_id, electronics_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[electronics_id] = new_electronics
            self.load_electronics()
            messagebox.showinfo("Success", f"Created new electronics: {new_electronics.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create electronics: {e}")

    def delete_electronics(self):
        """Delete the selected electronics."""
        selection = self.electronics_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No electronics selected to delete")
            return

        electronics = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "electronics") or \
               (hasattr(outfit, '__class__') and 'Electronics' in str(outfit.__class__)):
                electronics.append((outfit_id, outfit))

        if selection[0] < len(electronics):
            electronics_id, electronic = electronics[selection[0]]
            result = messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{electronic.name}'?")
            if result:
                del OUTFITS_REGISTRY[electronics_id]
                self.load_electronics()
                messagebox.showinfo("Success", f"Deleted electronics: {electronic.name}")

    def export_electronics(self):
        """Export electronics to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Electronics",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                electronics_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    if (hasattr(outfit, 'category') and outfit.category == "electronics") or \
                       (hasattr(outfit, '__class__') and 'Electronics' in str(outfit.__class__)):
                        electronics_data[outfit_id] = {
                            'name': outfit.name,
                            'category': getattr(outfit, 'category', 'electronics'),
                            'subcategory': getattr(outfit, 'subcategory', 'sensors'),
                            'cost': getattr(outfit, 'cost', 1000),
                            'space_required': getattr(outfit, 'space_required', 1),
                            'sensor_range_boost': getattr(outfit, 'sensor_range_boost', 0.0),
                            'targeting_boost': getattr(outfit, 'targeting_boost', 0.0),
                            'jamming_strength': getattr(outfit, 'jamming_strength', 0.0),
                            'energy_drain': getattr(outfit, 'energy_drain', 0.0)
                        }

                with open(filename, 'w') as f:
                    json.dump(electronics_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(electronics_data)} electronics to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export electronics: {e}")

    def create_utility_tab(self):
        """Create comprehensive utility tab."""
        utility_frame = ttk.Frame(self.notebook)
        self.notebook.add(utility_frame, text="Utility")

        # Create horizontal layout
        paned = ttk.PanedWindow(utility_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Utility list
        left_frame = ttk.LabelFrame(paned, text="Utility Library")
        paned.add(left_frame, weight=1)

        # Utility listbox
        self.utility_listbox = tk.Listbox(left_frame)
        self.utility_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.utility_listbox.bind("<<ListboxSelect>>", self.on_utility_select)

        # Buttons for utility management
        utility_button_frame = ttk.Frame(left_frame)
        utility_button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(utility_button_frame, text="New Utility", command=self.create_new_utility).pack(side=tk.LEFT, padx=2)
        ttk.Button(utility_button_frame, text="Delete", command=self.delete_utility).pack(side=tk.LEFT, padx=2)
        ttk.Button(utility_button_frame, text="Export", command=self.export_utility).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Utility Editor")
        paned.add(right_frame, weight=2)

        self.setup_utility_editor(right_frame)

    def load_utility(self):
        """Load utility outfits."""
        if hasattr(self, 'utility_listbox'):
            self.utility_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                if (hasattr(outfit, 'category') and outfit.category == "utility") or \
                   (hasattr(outfit, '__class__') and 'Utility' in str(outfit.__class__)):
                    subcategory = getattr(outfit, 'subcategory', 'unknown')
                    display_name = f"{outfit.name} ({subcategory})"
                    self.utility_listbox.insert(tk.END, display_name)

    def setup_utility_editor(self, parent):
        """Setup the utility editor interface."""
        # Basic Properties
        basic_frame = ttk.LabelFrame(parent, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.utility_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.utility_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.utility_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=100, to=100000, textvariable=self.utility_cost_var, width=10).grid(row=0, column=3, padx=5)

        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Space Required:").grid(row=0, column=0, sticky=tk.W)
        self.utility_space_var = tk.IntVar()
        ttk.Spinbox(row2, from_=1, to=20, textvariable=self.utility_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Type:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.utility_type_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.utility_type_var, values=["cargo", "fuel", "power", "crew", "life_support"], width=15).grid(row=0, column=3, padx=5)

        # Utility Properties
        utility_frame = ttk.LabelFrame(parent, text="Utility Properties")
        utility_frame.pack(fill=tk.X, padx=5, pady=5)

        utility_grid = ttk.Frame(utility_frame)
        utility_grid.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(utility_grid, text="Cargo Space Boost:").grid(row=0, column=0, sticky=tk.W)
        self.utility_cargo_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=500, textvariable=self.utility_cargo_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(utility_grid, text="Fuel Capacity Boost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.utility_fuel_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=1000, textvariable=self.utility_fuel_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(utility_grid, text="Energy Generation:").grid(row=1, column=0, sticky=tk.W)
        self.utility_energy_gen_var = tk.DoubleVar()
        ttk.Spinbox(utility_grid, from_=0.0, to=50.0, increment=0.1, textvariable=self.utility_energy_gen_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(utility_grid, text="Crew Capacity Boost:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.utility_crew_var = tk.IntVar()
        ttk.Spinbox(utility_grid, from_=0, to=100, textvariable=self.utility_crew_var, width=10).grid(row=1, column=3, padx=5)

        # Save button
        save_frame = ttk.Frame(parent)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Utility", command=self.save_utility).pack(side=tk.RIGHT, padx=5)

    def on_utility_select(self, event=None):
        """Handle utility selection from list."""
        selection = self.utility_listbox.curselection()
        if not selection:
            return

        utilities = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "utility") or \
               (hasattr(outfit, '__class__') and 'Utility' in str(outfit.__class__)):
                utilities.append(outfit)

        if selection[0] < len(utilities):
            utility = utilities[selection[0]]
            self.load_utility_into_editor(utility)

    def load_utility_into_editor(self, utility):
        """Load utility data into the editor."""
        self.current_outfit = utility

        self.utility_name_var.set(utility.name)
        self.utility_cost_var.set(getattr(utility, 'cost', 1000))
        self.utility_space_var.set(getattr(utility, 'space_required', 1))
        self.utility_type_var.set(getattr(utility, 'subcategory', 'cargo'))
        self.utility_cargo_var.set(getattr(utility, 'cargo_space_boost', 0))
        self.utility_fuel_var.set(getattr(utility, 'fuel_capacity_boost', 0))
        self.utility_energy_gen_var.set(getattr(utility, 'energy_generation', 0.0))
        self.utility_crew_var.set(getattr(utility, 'crew_capacity_boost', 0))

    def save_utility(self):
        """Save the current utility with editor values."""
        if not self.current_outfit:
            messagebox.showwarning("Warning", "No utility selected to save")
            return

        try:
            self.current_outfit.name = self.utility_name_var.get()
            if hasattr(self.current_outfit, 'cost'):
                self.current_outfit.cost = self.utility_cost_var.get()
            if hasattr(self.current_outfit, 'space_required'):
                self.current_outfit.space_required = self.utility_space_var.get()
            if hasattr(self.current_outfit, 'subcategory'):
                self.current_outfit.subcategory = self.utility_type_var.get()
            if hasattr(self.current_outfit, 'cargo_space_boost'):
                self.current_outfit.cargo_space_boost = self.utility_cargo_var.get()
            if hasattr(self.current_outfit, 'fuel_capacity_boost'):
                self.current_outfit.fuel_capacity_boost = self.utility_fuel_var.get()
            if hasattr(self.current_outfit, 'energy_generation'):
                self.current_outfit.energy_generation = self.utility_energy_gen_var.get()
            if hasattr(self.current_outfit, 'crew_capacity_boost'):
                self.current_outfit.crew_capacity_boost = self.utility_crew_var.get()

            messagebox.showinfo("Success", f"Saved utility: {self.current_outfit.name}")
            self.load_utility()
            self.auto_save_outfits()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save utility: {e}")

    def create_new_utility(self):
        """Create a new utility."""
        utility_id = simpledialog.askstring("New Utility", "Enter a unique ID for the new utility:")
        if not utility_id:
            return

        if utility_id in OUTFITS_REGISTRY:
            messagebox.showerror("Error", f"A utility with ID '{utility_id}' already exists")
            return

        try:
            if 'UtilityOutfit' in globals():
                new_utility = UtilityOutfit(utility_id, utility_id.replace('_', ' ').title(), "cargo")
            else:
                class SimpleUtility:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.category = "utility"
                        self.subcategory = "cargo"
                        self.cost = 1000
                        self.space_required = 1
                        self.cargo_space_boost = 0
                        self.fuel_capacity_boost = 0
                        self.energy_generation = 0.0
                        self.crew_capacity_boost = 0

                new_utility = SimpleUtility(utility_id, utility_id.replace('_', ' ').title())

            OUTFITS_REGISTRY[utility_id] = new_utility
            self.load_utility()
            messagebox.showinfo("Success", f"Created new utility: {new_utility.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create utility: {e}")

    def delete_utility(self):
        """Delete the selected utility."""
        selection = self.utility_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No utility selected to delete")
            return

        utilities = []
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            if (hasattr(outfit, 'category') and outfit.category == "utility") or \
               (hasattr(outfit, '__class__') and 'Utility' in str(outfit.__class__)):
                utilities.append((outfit_id, outfit))

        if selection[0] < len(utilities):
            utility_id, utility = utilities[selection[0]]
            result = messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{utility.name}'?")
            if result:
                del OUTFITS_REGISTRY[utility_id]
                self.load_utility()
                messagebox.showinfo("Success", f"Deleted utility: {utility.name}")

    def export_utility(self):
        """Export utility outfits to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Utility Outfits",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                utility_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    if (hasattr(outfit, 'category') and outfit.category == "utility") or \
                       (hasattr(outfit, '__class__') and 'Utility' in str(outfit.__class__)):
                        utility_data[outfit_id] = {
                            'name': outfit.name,
                            'category': getattr(outfit, 'category', 'utility'),
                            'subcategory': getattr(outfit, 'subcategory', 'cargo'),
                            'cost': getattr(outfit, 'cost', 1000),
                            'space_required': getattr(outfit, 'space_required', 1),
                            'cargo_space_boost': getattr(outfit, 'cargo_space_boost', 0),
                            'fuel_capacity_boost': getattr(outfit, 'fuel_capacity_boost', 0),
                            'energy_generation': getattr(outfit, 'energy_generation', 0.0),
                            'crew_capacity_boost': getattr(outfit, 'crew_capacity_boost', 0)
                        }

                with open(filename, 'w') as f:
                    json.dump(utility_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(utility_data)} utility outfits to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export utility outfits: {e}")

    def auto_save_outfits(self):
        """Auto-save outfit changes to JSON file."""
        try:
            save_file = "outfits_data.json"
            outfit_data = {}

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                outfit_data[outfit_id] = {
                    'name': outfit.name,
                    'category': getattr(outfit, 'category', 'unknown'),
                    'subcategory': getattr(outfit, 'subcategory', ''),
                    'cost': getattr(outfit, 'cost', 0),
                    'space_required': getattr(outfit, 'space_required', 0),
                    'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                    'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                    'description': getattr(outfit, 'description', '')
                }

                # Add category-specific properties
                if outfit.category == "weapons":
                    outfit_data[outfit_id].update({
                        'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                        'damage': getattr(outfit, 'damage', 0),
                        'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                        'range': getattr(outfit, 'range', 0),
                        'energy_usage': getattr(outfit, 'energy_usage', 0),
                        'uses_ammo': getattr(outfit, 'uses_ammo', False),
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'max_ammo': getattr(outfit, 'max_ammo', 0)
                    })
                elif outfit.category == "ammunition":
                    outfit_data[outfit_id].update({
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'quantity': getattr(outfit, 'quantity', 0),
                        'damage': getattr(outfit, 'damage', 0),
                        'projectile_speed': getattr(outfit, 'projectile_speed', 0),
                        'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                        'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                        'explosion_radius': getattr(outfit, 'explosion_radius', 0)
                    })
                elif outfit.category == "defense":
                    outfit_data[outfit_id].update({
                        'shield_boost': getattr(outfit, 'shield_boost', 0),
                        'armor_boost': getattr(outfit, 'armor_boost', 0),
                        'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                        'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                    })

            with open(save_file, 'w') as f:
                json.dump(outfit_data, f, indent=2)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to auto-save outfits: {e}")

    def save_all_outfits(self):
        """Save all outfits to JSON file."""
        try:
            self.auto_save_outfits()
            messagebox.showinfo("Success", "All outfits saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save outfits: {e}")


def main():
    """Main entry point for the enhanced editor."""
    root = tk.Tk()
    editor = EnhancedEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()0.0),
                            'crew_capacity_boost': getattr(outfit, 'crew_capacity_boost', 0)
                        }

                with open(filename, 'w') as f:
                    json.dump(utility_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(utility_data)} utility outfits to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export utility outfits: {e}")

    def auto_save_outfits(self):
        """Auto-save outfit changes to JSON file."""
        try:
            save_file = "outfits_data.json"
            outfit_data = {}

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                outfit_data[outfit_id] = {
                    'name': outfit.name,
                    'category': getattr(outfit, 'category', 'unknown'),
                    'subcategory': getattr(outfit, 'subcategory', ''),
                    'cost': getattr(outfit, 'cost', 0),
                    'space_required': getattr(outfit, 'space_required', 0),
                    'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                    'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                    'description': getattr(outfit, 'description', '')
                }

                # Add category-specific properties
                if outfit.category == "weapons":
                    outfit_data[outfit_id].update({
                        'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                        'damage': getattr(outfit, 'damage', 0),
                        'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                        'range': getattr(outfit, 'range', 0),
                        'energy_usage': getattr(outfit, 'energy_usage', 0),
                        'uses_ammo': getattr(outfit, 'uses_ammo', False),
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'max_ammo': getattr(outfit, 'max_ammo', 0)
                    })
                elif outfit.category == "ammunition":
                    outfit_data[outfit_id].update({
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'quantity': getattr(outfit, 'quantity', 0),
                        'damage': getattr(outfit, 'damage', 0),
                        'projectile_speed': getattr(outfit, 'projectile_speed', 0),
                        'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                        'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                        'explosion_radius': getattr(outfit, 'explosion_radius', 0)
                    })
                elif outfit.category == "defense":
                    outfit_data[outfit_id].update({
                        'shield_boost': getattr(outfit, 'shield_boost', 0),
                        'armor_boost': getattr(outfit, 'armor_boost', 0),
                        'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                        'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                    })

            with open(save_file, 'w') as f:
                json.dump(outfit_data, f, indent=2)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to auto-save outfits: {e}")

    def save_all_outfits(self):
        """Save all outfits to JSON file."""
        try:
            self.auto_save_outfits()
            messagebox.showinfo("Success", "All outfits saved successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save outfits: {e}")


    def create_ships_tab(self):
        """Create comprehensive ships tab with editor."""
        ships_frame = ttk.Frame(self.notebook)
        self.notebook.add(ships_frame, text="Ships")
        
        # Create horizontal layout
        paned = ttk.PanedWindow(ships_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Ship list
        left_frame = ttk.LabelFrame(paned, text="Ship Library")
        paned.add(left_frame, weight=1)
        
        # Ship listbox
        self.ship_listbox = tk.Listbox(left_frame)
        self.ship_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.ship_listbox.bind("<<ListboxSelect>>", self.on_ship_select)
        
        # Right panel - Ship editor placeholder
        right_frame = ttk.LabelFrame(paned, text="Ship Editor")
        paned.add(right_frame, weight=2)
        
        # Placeholder for ship editor
        ttk.Label(right_frame, text="Ship editor coming soon!").pack(padx=10, pady=10)
        
    def on_ship_select(self, event=None):
        """Handle ship selection from list."""
        # Placeholder for ship selection handling
        pass


def main():
    """Main entry point for the enhanced editor."""
    root = tk.Tk()
    editor = EnhancedEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()

        # Create horizontal layout
        paned = ttk.PanedWindow(ships_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Ship list
        left_frame = ttk.LabelFrame(paned, text="Ships Library")
        paned.add(left_frame, weight=1)

        # Ship listbox
        self.ship_listbox = tk.Listbox(left_frame)
        self.ship_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))
        self.ship_listbox.bind("<<ListboxSelect>>", self.on_ship_select)

        # Buttons for ship management
        ship_button_frame = ttk.Frame(left_frame)
        ship_button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(ship_button_frame, text="New Ship", command=self.create_new_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(ship_button_frame, text="Delete", command=self.delete_ship).pack(side=tk.LEFT, padx=2)
        ttk.Button(ship_button_frame, text="Export", command=self.export_ships).pack(side=tk.LEFT, padx=2)
        ttk.Button(ship_button_frame, text="Save All", command=self.save_all_ships).pack(side=tk.LEFT, padx=2)

        # Right panel - Editor
        right_frame = ttk.LabelFrame(paned, text="Ship Editor")
        paned.add(right_frame, weight=2)

        self.setup_ship_editor(right_frame)

    def setup_ship_editor(self, parent):
        """Setup the comprehensive ship editor interface."""
        # Create scrollable frame for all the ship properties
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollbar and canvas
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Basic Properties
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Properties")
        basic_frame.pack(fill=tk.X, padx=5, pady=5)

        # Name, Class, Size
        row1 = ttk.Frame(basic_frame)
        row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row1, text="Name:").grid(row=0, column=0, sticky=tk.W)
        self.ship_name_var = tk.StringVar()
        ttk.Entry(row1, textvariable=self.ship_name_var, width=25).grid(row=0, column=1, padx=5)

        ttk.Label(row1, text="Cost:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_cost_var = tk.IntVar()
        ttk.Spinbox(row1, from_=1000, to=10000000, textvariable=self.ship_cost_var, width=12).grid(row=0, column=3, padx=5)

        # Ship Class and Size
        row2 = ttk.Frame(basic_frame)
        row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row2, text="Ship Class:").grid(row=0, column=0, sticky=tk.W)
        self.ship_class_var = tk.StringVar()
        ship_classes = ["fighter", "freighter", "transport", "corvette", "frigate", "cruiser", "destroyer", "carrier", "battleship", "dreadnought"]
        ttk.Combobox(row2, textvariable=self.ship_class_var, values=ship_classes, width=15).grid(row=0, column=1, padx=5)

        ttk.Label(row2, text="Size:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_size_var = tk.StringVar()
        ttk.Combobox(row2, textvariable=self.ship_size_var, values=self.ship_sizes, width=10).grid(row=0, column=3, padx=5)

        # Tech Level and Manufacturer
        row3 = ttk.Frame(basic_frame)
        row3.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(row3, text="Tech Level:").grid(row=0, column=0, sticky=tk.W)
        self.ship_tech_level_var = tk.IntVar()
        ttk.Spinbox(row3, from_=1, to=10, textvariable=self.ship_tech_level_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(row3, text="Manufacturer:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_manufacturer_var = tk.StringVar()
        ttk.Entry(row3, textvariable=self.ship_manufacturer_var, width=20).grid(row=0, column=3, padx=5)

        # Performance Stats
        performance_frame = ttk.LabelFrame(scrollable_frame, text="Performance Stats")
        performance_frame.pack(fill=tk.X, padx=5, pady=5)

        perf_grid = ttk.Frame(performance_frame)
        perf_grid.pack(fill=tk.X, padx=5, pady=5)

        # Speed and Acceleration
        ttk.Label(perf_grid, text="Max Speed:").grid(row=0, column=0, sticky=tk.W)
        self.ship_max_speed_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.5, to=20.0, increment=0.1, textvariable=self.ship_max_speed_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(perf_grid, text="Acceleration:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_acceleration_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.01, to=5.0, increment=0.01, textvariable=self.ship_acceleration_var, width=10).grid(row=0, column=3, padx=5)

        # Turn Rate and Maneuverability
        ttk.Label(perf_grid, text="Turn Rate:").grid(row=1, column=0, sticky=tk.W)
        self.ship_turn_rate_var = tk.DoubleVar()
        ttk.Spinbox(perf_grid, from_=0.01, to=10.0, increment=0.01, textvariable=self.ship_turn_rate_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(perf_grid, text="Maneuverability:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_maneuverability_var = tk.StringVar()
        ttk.Combobox(perf_grid, textvariable=self.ship_maneuverability_var, values=["light", "medium", "heavy"], width=10).grid(row=1, column=3, padx=5)

        # Capacity Stats
        capacity_frame = ttk.LabelFrame(scrollable_frame, text="Capacity & Space")
        capacity_frame.pack(fill=tk.X, padx=5, pady=5)

        cap_grid = ttk.Frame(capacity_frame)
        cap_grid.pack(fill=tk.X, padx=5, pady=5)

        # Cargo and Outfit Space
        ttk.Label(cap_grid, text="Cargo Space:").grid(row=0, column=0, sticky=tk.W)
        self.ship_cargo_space_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=0, to=1000, textvariable=self.ship_cargo_space_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(cap_grid, text="Outfit Space:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_outfit_space_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=0, to=500, textvariable=self.ship_outfit_space_var, width=10).grid(row=0, column=3, padx=5)

        # Crew and Passengers
        ttk.Label(cap_grid, text="Crew Capacity:").grid(row=1, column=0, sticky=tk.W)
        self.ship_crew_capacity_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=1, to=1000, textvariable=self.ship_crew_capacity_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(cap_grid, text="Passenger Cap.:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_passenger_capacity_var = tk.IntVar()
        ttk.Spinbox(cap_grid, from_=0, to=500, textvariable=self.ship_passenger_capacity_var, width=10).grid(row=1, column=3, padx=5)

        # Defense Stats
        defense_frame = ttk.LabelFrame(scrollable_frame, text="Defense Stats")
        defense_frame.pack(fill=tk.X, padx=5, pady=5)

        def_grid = ttk.Frame(defense_frame)
        def_grid.pack(fill=tk.X, padx=5, pady=5)

        # Shields and Armor
        ttk.Label(def_grid, text="Shields:").grid(row=0, column=0, sticky=tk.W)
        self.ship_shields_var = tk.IntVar()
        ttk.Spinbox(def_grid, from_=0, to=10000, textvariable=self.ship_shields_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(def_grid, text="Armor:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_armor_var = tk.IntVar()
        ttk.Spinbox(def_grid, from_=1, to=10000, textvariable=self.ship_armor_var, width=10).grid(row=0, column=3, padx=5)

        # Shield Recharge and Mass
        ttk.Label(def_grid, text="Shield Recharge:").grid(row=1, column=0, sticky=tk.W)
        self.ship_shield_recharge_var = tk.DoubleVar()
        ttk.Spinbox(def_grid, from_=0.1, to=50.0, increment=0.1, textvariable=self.ship_shield_recharge_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(def_grid, text="Mass:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_mass_var = tk.DoubleVar()
        ttk.Spinbox(def_grid, from_=0.1, to=100.0, increment=0.1, textvariable=self.ship_mass_var, width=10).grid(row=1, column=3, padx=5)

        # Energy & Resources
        energy_frame = ttk.LabelFrame(scrollable_frame, text="Energy & Resources")
        energy_frame.pack(fill=tk.X, padx=5, pady=5)

        energy_grid = ttk.Frame(energy_frame)
        energy_grid.pack(fill=tk.X, padx=5, pady=5)

        # Energy Capacity and Recharge
        ttk.Label(energy_grid, text="Energy Capacity:").grid(row=0, column=0, sticky=tk.W)
        self.ship_energy_capacity_var = tk.IntVar()
        ttk.Spinbox(energy_grid, from_=10, to=10000, textvariable=self.ship_energy_capacity_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(energy_grid, text="Energy Recharge:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_energy_recharge_var = tk.DoubleVar()
        ttk.Spinbox(energy_grid, from_=0.1, to=100.0, increment=0.1, textvariable=self.ship_energy_recharge_var, width=10).grid(row=0, column=3, padx=5)

        # Fuel Capacity and Engine Efficiency
        ttk.Label(energy_grid, text="Fuel Capacity:").grid(row=1, column=0, sticky=tk.W)
        self.ship_fuel_capacity_var = tk.IntVar()
        ttk.Spinbox(energy_grid, from_=10, to=10000, textvariable=self.ship_fuel_capacity_var, width=10).grid(row=1, column=1, padx=5)

        ttk.Label(energy_grid, text="Engine Efficiency:").grid(row=1, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_engine_efficiency_var = tk.DoubleVar()
        ttk.Spinbox(energy_grid, from_=0.1, to=10.0, increment=0.1, textvariable=self.ship_engine_efficiency_var, width=10).grid(row=1, column=3, padx=5)

        # Hardpoints System
        hardpoints_frame = ttk.LabelFrame(scrollable_frame, text="Hardpoints System")
        hardpoints_frame.pack(fill=tk.X, padx=5, pady=5)

        hard_grid = ttk.Frame(hardpoints_frame)
        hard_grid.pack(fill=tk.X, padx=5, pady=5)

        # Weapon and Turret Hardpoints
        ttk.Label(hard_grid, text="Weapon Hardpoints:").grid(row=0, column=0, sticky=tk.W)
        self.ship_weapon_hardpoints_var = tk.IntVar()
        ttk.Spinbox(hard_grid, from_=0, to=50, textvariable=self.ship_weapon_hardpoints_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(hard_grid, text="Turret Hardpoints:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_turret_hardpoints_var = tk.IntVar()
        ttk.Spinbox(hard_grid, from_=0, to=50, textvariable=self.ship_turret_hardpoints_var, width=10).grid(row=0, column=3, padx=5)

        ttk.Label(hard_grid, text="Engine Hardpoints:").grid(row=1, column=0, sticky=tk.W)
        self.ship_engine_hardpoints_var = tk.IntVar()
        ttk.Spinbox(hard_grid, from_=0, to=20, textvariable=self.ship_engine_hardpoints_var, width=10).grid(row=1, column=1, padx=5)

        # Visual Assets & Animation
        visual_frame = ttk.LabelFrame(scrollable_frame, text="Visual Assets & Animation")
        visual_frame.pack(fill=tk.X, padx=5, pady=5)

        # Game Sprite
        sprite_row1 = ttk.Frame(visual_frame)
        sprite_row1.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(sprite_row1, text="Game Sprite:").grid(row=0, column=0, sticky=tk.W)
        self.ship_game_sprite_var = tk.StringVar()
        ttk.Entry(sprite_row1, textvariable=self.ship_game_sprite_var, width=30).grid(row=0, column=1, padx=5)
        ttk.Button(sprite_row1, text="Browse", command=self.browse_game_sprite).grid(row=0, column=2, padx=2)

        # Shipyard Sprite
        sprite_row2 = ttk.Frame(visual_frame)
        sprite_row2.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(sprite_row2, text="Shipyard Sprite:").grid(row=0, column=0, sticky=tk.W)
        self.ship_shipyard_sprite_var = tk.StringVar()
        ttk.Entry(sprite_row2, textvariable=self.ship_shipyard_sprite_var, width=30).grid(row=0, column=1, padx=5)
        ttk.Button(sprite_row2, text="Browse", command=self.browse_shipyard_sprite).grid(row=0, column=2, padx=2)

        # Animation Frames and Sprite Size
        sprite_row3 = ttk.Frame(visual_frame)
        sprite_row3.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(sprite_row3, text="Animation Frames:").grid(row=0, column=0, sticky=tk.W)
        self.ship_animation_frames_var = tk.IntVar()
        ttk.Spinbox(sprite_row3, from_=1, to=360, textvariable=self.ship_animation_frames_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(sprite_row3, text="Sprite Size:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_sprite_size_var = tk.IntVar()
        ttk.Spinbox(sprite_row3, from_=16, to=256, textvariable=self.ship_sprite_size_var, width=10).grid(row=0, column=3, padx=5)

        # Spritesheet Animation Control
        anim_frame = ttk.LabelFrame(visual_frame, text="Spritesheet Animation Control")
        anim_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Animation Type
        anim_row1 = ttk.Frame(anim_frame)
        anim_row1.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(anim_row1, text="Animation Type:").grid(row=0, column=0, sticky=tk.W)
        self.ship_anim_type_var = tk.StringVar()
        ttk.Combobox(anim_row1, textvariable=self.ship_anim_type_var, 
                    values=["rotation", "thruster", "static", "custom"], width=15).grid(row=0, column=1, padx=5)
        
        ttk.Label(anim_row1, text="Frame Rate (FPS):").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_frame_rate_var = tk.IntVar()
        ttk.Spinbox(anim_row1, from_=1, to=60, textvariable=self.ship_frame_rate_var, width=10).grid(row=0, column=3, padx=5)
        
        # Animation Sequences
        anim_row2 = ttk.Frame(anim_frame)
        anim_row2.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(anim_row2, text="Idle Sequence:").grid(row=0, column=0, sticky=tk.W)
        self.ship_idle_sequence_var = tk.StringVar()
        ttk.Entry(anim_row2, textvariable=self.ship_idle_sequence_var, width=20).grid(row=0, column=1, padx=5)
        ttk.Label(anim_row2, text="(e.g., 0-5)").grid(row=0, column=2, padx=2)
        
        anim_row3 = ttk.Frame(anim_frame)
        anim_row3.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(anim_row3, text="Thrust Sequence:").grid(row=0, column=0, sticky=tk.W)
        self.ship_thrust_sequence_var = tk.StringVar()
        ttk.Entry(anim_row3, textvariable=self.ship_thrust_sequence_var, width=20).grid(row=0, column=1, padx=5)
        ttk.Label(anim_row3, text="(e.g., 6-11)").grid(row=0, column=2, padx=2)
        
        # Preview button
        ttk.Button(anim_frame, text="Preview Animation", command=self.preview_ship_animation).pack(pady=5)

        # Special Features
        special_frame = ttk.LabelFrame(scrollable_frame, text="Special Features")
        special_frame.pack(fill=tk.X, padx=5, pady=5)

        # Ship Roles
        roles_row = ttk.Frame(special_frame)
        roles_row.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(roles_row, text="Ship Roles:").grid(row=0, column=0, sticky=tk.W)
        self.ship_roles_var = tk.StringVar()
        ttk.Entry(roles_row, textvariable=self.ship_roles_var, width=40).grid(row=0, column=1, padx=5)
        ttk.Label(roles_row, text="(comma-separated)").grid(row=0, column=2, padx=5)

        # Special Abilities
        abilities_row = ttk.Frame(special_frame)
        abilities_row.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(abilities_row, text="Special Abilities:").grid(row=0, column=0, sticky=tk.W)
        self.ship_special_abilities_var = tk.StringVar()
        ttk.Entry(abilities_row, textvariable=self.ship_special_abilities_var, width=40).grid(row=0, column=1, padx=5)
        ttk.Label(abilities_row, text="(comma-separated)").grid(row=0, column=2, padx=5)

        # Requirements
        requirements_frame = ttk.LabelFrame(scrollable_frame, text="Purchase Requirements")
        requirements_frame.pack(fill=tk.X, padx=5, pady=5)

        req_grid = ttk.Frame(requirements_frame)
        req_grid.pack(fill=tk.X, padx=5, pady=5)

        # Required Reputation and Hyperspace Speed
        ttk.Label(req_grid, text="Required Reputation:").grid(row=0, column=0, sticky=tk.W)
        self.ship_required_reputation_var = tk.IntVar()
        ttk.Spinbox(req_grid, from_=-1000, to=1000, textvariable=self.ship_required_reputation_var, width=10).grid(row=0, column=1, padx=5)

        ttk.Label(req_grid, text="Hyperspace Speed:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        self.ship_hyperspace_speed_var = tk.DoubleVar()
        ttk.Spinbox(req_grid, from_=0.1, to=10.0, increment=0.1, textvariable=self.ship_hyperspace_speed_var, width=10).grid(row=0, column=3, padx=5)

        # Description
        description_frame = ttk.LabelFrame(scrollable_frame, text="Description & Lore")
        description_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(description_frame, text="Description:").pack(anchor=tk.W, padx=5, pady=(5, 0))
        self.ship_description_text = tk.Text(description_frame, height=4, wrap=tk.WORD)
        self.ship_description_text.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Origin
        origin_row = ttk.Frame(description_frame)
        origin_row.pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(origin_row, text="Origin:").grid(row=0, column=0, sticky=tk.W)
        self.ship_origin_var = tk.StringVar()
        ttk.Entry(origin_row, textvariable=self.ship_origin_var, width=30).grid(row=0, column=1, padx=5)

        # Save button
        save_frame = ttk.Frame(scrollable_frame)
        save_frame.pack(fill=tk.X, padx=5, pady=10)

        ttk.Button(save_frame, text="Save Ship", command=self.save_ship).pack(side=tk.RIGHT, padx=5)

    def save_all_outfits(self):
        """Save all outfits to a JSON file for persistence."""
        filename = filedialog.asksaveasfilename(
            title="Save All Outfits",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialvalue="outfits_data.json"
        )
        if filename:
            try:
                outfits_data = {}
                for outfit_id, outfit in OUTFITS_REGISTRY.items():
                    outfit_data = {
                        'id': outfit.id,
                        'name': outfit.name,
                        'category': outfit.category,
                        'subcategory': getattr(outfit, 'subcategory', ''),
                        'cost': getattr(outfit, 'cost', 1000),
                        'space_required': getattr(outfit, 'space_required', 1),
                        'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                        'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                        'description': getattr(outfit, 'description', ''),
                    }

                    # Add category-specific properties
                    if outfit.category == "weapons":
                        outfit_data.update({
                            'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                            'damage': getattr(outfit, 'damage', 10),
                            'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                            'range': getattr(outfit, 'range', 300),
                            'energy_usage': getattr(outfit, 'energy_usage', 5),
                            'uses_ammo': getattr(outfit, 'uses_ammo', False),
                            'ammo_type': getattr(outfit, 'ammo_type', ''),
                            'max_ammo': getattr(outfit, 'max_ammo', 0)
                        })
                    elif outfit.category == "ammunition":
                        outfit_data.update({
                            'ammo_type': getattr(outfit, 'ammo_type', ''),
                            'quantity': getattr(outfit, 'quantity', 10),
                            'damage': getattr(outfit, 'damage', 50),
                            'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                            'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                            'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                            'explosion_radius': getattr(outfit, 'explosion_radius', 20)
                        })
                    elif outfit.category == "defense":
                        outfit_data.update({
                            'shield_boost': getattr(outfit, 'shield_boost', 0),
                            'armor_boost': getattr(outfit, 'armor_boost', 0),
                            'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                            'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                        })

                    outfits_data[outfit_id] = outfit_data

                with open(filename, 'w') as f:
                    json.dump(outfits_data, f, indent=2)

                messagebox.showinfo("Success", f"Saved {len(outfits_data)} outfits to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save outfits: {e}")

    def auto_save_outfits(self):
        """Automatically save outfits to the default file."""
        try:
            outfits_data = {}
            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                outfit_data = {
                    'id': outfit.id,
                    'name': outfit.name,
                    'category': outfit.category,
                    'subcategory': getattr(outfit, 'subcategory', ''),
                    'cost': getattr(outfit, 'cost', 1000),
                    'space_required': getattr(outfit, 'space_required', 1),
                    'min_tech_level': getattr(outfit, 'min_tech_level', 1),
                    'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
                    'description': getattr(outfit, 'description', ''),
                }

                # Add category-specific properties
                if outfit.category == "weapons":
                    outfit_data.update({
                        'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                        'damage': getattr(outfit, 'damage', 10),
                        'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                        'range': getattr(outfit, 'range', 300),
                        'energy_usage': getattr(outfit, 'energy_usage', 5),
                        'uses_ammo': getattr(outfit, 'uses_ammo', False),
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'max_ammo': getattr(outfit, 'max_ammo', 0)
                    })
                elif outfit.category == "ammunition":
                    outfit_data.update({
                        'ammo_type': getattr(outfit, 'ammo_type', ''),
                        'quantity': getattr(outfit, 'quantity', 10),
                        'damage': getattr(outfit, 'damage', 50),
                        'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                        'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                        'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                        'explosion_radius': getattr(outfit, 'explosion_radius', 20)
                    })
                elif outfit.category == "defense":
                    outfit_data.update({
                        'shield_boost': getattr(outfit, 'shield_boost', 0),
                        'armor_boost': getattr(outfit, 'armor_boost', 0),
                        'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                        'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                    })

                outfits_data[outfit_id] = outfit_data

            with open("outfits_data.json", 'w') as f:
                json.dump(outfits_data, f, indent=2)

            print(f"Auto-saved {len(outfits_data)} outfits to outfits_data.json")

        except Exception as e:
            print(f"Auto-save failed: {e}")

    # =========================================================================
    # SHIP EDITOR METHODS - Complete ship editing functionality
    # =========================================================================

    def load_ships(self):
        """Load ships into the listbox."""
        if hasattr(self, 'ship_listbox'):
            self.ship_listbox.delete(0, tk.END)
            
            # Use SHIPS registry or STANDARDIZED_SHIPS_REGISTRY
            ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
            
            for ship_id, ship in ships_source.items():
                ship_class = getattr(ship, 'ship_class', 'unknown')
                size = getattr(ship, 'size', 'unknown')
                display_name = f"{ship.name} ({ship_class}, {size})"
                self.ship_listbox.insert(tk.END, display_name)

    def on_ship_select(self, event=None):
        """Handle ship selection from list."""
        selection = self.ship_listbox.curselection()
        if not selection:
            return

        # Get selected ship
        ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
        ships_list = list(ships_source.values())

        if selection[0] < len(ships_list):
            ship = ships_list[selection[0]]
            self.load_ship_into_editor(ship)

    def load_ship_into_editor(self, ship):
        """Load ship data into the editor."""
        self.current_ship = ship

        # Load basic properties
        self.ship_name_var.set(ship.name)
        self.ship_cost_var.set(getattr(ship, 'cost', 10000))
        self.ship_class_var.set(getattr(ship, 'ship_class', 'fighter'))
        self.ship_size_var.set(getattr(ship, 'size', 'small'))
        self.ship_tech_level_var.set(getattr(ship, 'min_tech_level', 1))
        self.ship_manufacturer_var.set(getattr(ship, 'manufacturer', 'Unknown'))

        # Load performance stats
        self.ship_max_speed_var.set(getattr(ship, 'max_speed', 3.0))
        self.ship_acceleration_var.set(getattr(ship, 'acceleration', 0.5))
        self.ship_turn_rate_var.set(getattr(ship, 'turn_rate', 1.0))
        self.ship_maneuverability_var.set(getattr(ship, 'maneuverability_class', 'medium'))

        # Load capacity stats
        self.ship_cargo_space_var.set(getattr(ship, 'cargo_space', 10))
        self.ship_outfit_space_var.set(getattr(ship, 'outfit_space', 20))
        self.ship_crew_capacity_var.set(getattr(ship, 'crew_capacity', 1))
        self.ship_passenger_capacity_var.set(getattr(ship, 'passenger_capacity', 0))

        # Load defense stats
        self.ship_shields_var.set(getattr(ship, 'shields', 50))
        self.ship_armor_var.set(getattr(ship, 'armor', 30))
        self.ship_shield_recharge_var.set(getattr(ship, 'shield_recharge_rate', 1.0))
        self.ship_mass_var.set(getattr(ship, 'mass', 1.0))

        # Load energy & resources
        self.ship_energy_capacity_var.set(getattr(ship, 'energy_capacity', 100))
        self.ship_energy_recharge_var.set(getattr(ship, 'energy_recharge', 10.0))
        self.ship_fuel_capacity_var.set(getattr(ship, 'fuel_capacity', 100))
        self.ship_engine_efficiency_var.set(getattr(ship, 'engine_efficiency', 1.0))

        # Load hardpoints
        weapon_hardpoints = getattr(ship, 'weapon_hardpoints', [])
        turret_hardpoints = getattr(ship, 'turret_hardpoints', [])
        engine_hardpoints = getattr(ship, 'engine_hardpoints', [])
        
        self.ship_weapon_hardpoints_var.set(len(weapon_hardpoints))
        self.ship_turret_hardpoints_var.set(len(turret_hardpoints))
        self.ship_engine_hardpoints_var.set(len(engine_hardpoints))

        # Load visual assets
        self.ship_game_sprite_var.set(getattr(ship, 'game_sprite', ''))
        self.ship_shipyard_sprite_var.set(getattr(ship, 'shipyard_sprite', ''))
        self.ship_animation_frames_var.set(getattr(ship, 'animation_frames', 1))
        self.ship_sprite_size_var.set(getattr(ship, 'sprite_size', 32))
        
        # Load animation settings
        self.ship_anim_type_var.set(getattr(ship, 'animation_type', 'rotation'))
        self.ship_frame_rate_var.set(getattr(ship, 'frame_rate', 30))
        self.ship_idle_sequence_var.set(getattr(ship, 'idle_sequence', '0-0'))
        self.ship_thrust_sequence_var.set(getattr(ship, 'thrust_sequence', '0-0'))

        # Load special features
        ship_roles = getattr(ship, 'ship_roles', ['general'])
        special_abilities = getattr(ship, 'special_abilities', [])
        
        self.ship_roles_var.set(', '.join(ship_roles))
        self.ship_special_abilities_var.set(', '.join(special_abilities))

        # Load requirements
        self.ship_required_reputation_var.set(getattr(ship, 'required_reputation', 0))
        self.ship_hyperspace_speed_var.set(getattr(ship, 'hyperspace_speed', 1.0))

        # Load description and origin
        self.ship_description_text.delete(1.0, tk.END)
        self.ship_description_text.insert(1.0, getattr(ship, 'description', ''))
        self.ship_origin_var.set(getattr(ship, 'origin', 'Unknown'))

    def browse_game_sprite(self):
        """Browse for game sprite file."""
        filename = filedialog.askopenfilename(
            title="Select Game Sprite",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            try:
                relative_path = os.path.relpath(filename, os.getcwd())
                self.ship_game_sprite_var.set(relative_path)
            except ValueError:
                self.ship_game_sprite_var.set(filename)

    def browse_shipyard_sprite(self):
        """Browse for shipyard sprite file."""
        filename = filedialog.askopenfilename(
            title="Select Shipyard Sprite",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            try:
                relative_path = os.path.relpath(filename, os.getcwd())
                self.ship_shipyard_sprite_var.set(relative_path)
            except ValueError:
                self.ship_shipyard_sprite_var.set(filename)

    def save_ship(self):
        """Save the current ship with editor values."""
        if not self.current_ship:
            messagebox.showwarning("Warning", "No ship selected to save")
            return

        try:
            # Update basic properties
            self.current_ship.name = self.ship_name_var.get()
            if hasattr(self.current_ship, 'cost'):
                self.current_ship.cost = self.ship_cost_var.get()
            if hasattr(self.current_ship, 'ship_class'):
                self.current_ship.ship_class = self.ship_class_var.get()
            if hasattr(self.current_ship, 'size'):
                self.current_ship.size = self.ship_size_var.get()
            if hasattr(self.current_ship, 'min_tech_level'):
                self.current_ship.min_tech_level = self.ship_tech_level_var.get()
            if hasattr(self.current_ship, 'manufacturer'):
                self.current_ship.manufacturer = self.ship_manufacturer_var.get()

            # Update performance stats
            if hasattr(self.current_ship, 'max_speed'):
                self.current_ship.max_speed = self.ship_max_speed_var.get()
            if hasattr(self.current_ship, 'acceleration'):
                self.current_ship.acceleration = self.ship_acceleration_var.get()
            if hasattr(self.current_ship, 'turn_rate'):
                self.current_ship.turn_rate = self.ship_turn_rate_var.get()
            if hasattr(self.current_ship, 'maneuverability_class'):
                self.current_ship.maneuverability_class = self.ship_maneuverability_var.get()

            # Update capacity stats
            if hasattr(self.current_ship, 'cargo_space'):
                self.current_ship.cargo_space = self.ship_cargo_space_var.get()
            if hasattr(self.current_ship, 'outfit_space'):
                self.current_ship.outfit_space = self.ship_outfit_space_var.get()
            if hasattr(self.current_ship, 'crew_capacity'):
                self.current_ship.crew_capacity = self.ship_crew_capacity_var.get()
            if hasattr(self.current_ship, 'passenger_capacity'):
                self.current_ship.passenger_capacity = self.ship_passenger_capacity_var.get()

            # Update defense stats
            if hasattr(self.current_ship, 'shields'):
                self.current_ship.shields = self.ship_shields_var.get()
                self.current_ship.max_shields = self.ship_shields_var.get()
            if hasattr(self.current_ship, 'armor'):
                self.current_ship.armor = self.ship_armor_var.get()
                self.current_ship.max_armor = self.ship_armor_var.get()
            if hasattr(self.current_ship, 'shield_recharge_rate'):
                self.current_ship.shield_recharge_rate = self.ship_shield_recharge_var.get()
            if hasattr(self.current_ship, 'mass'):
                self.current_ship.mass = self.ship_mass_var.get()

            # Update energy & resources
            if hasattr(self.current_ship, 'energy_capacity'):
                self.current_ship.energy_capacity = self.ship_energy_capacity_var.get()
            if hasattr(self.current_ship, 'energy_recharge'):
                self.current_ship.energy_recharge = self.ship_energy_recharge_var.get()
            if hasattr(self.current_ship, 'fuel_capacity'):
                self.current_ship.fuel_capacity = self.ship_fuel_capacity_var.get()
            if hasattr(self.current_ship, 'engine_efficiency'):
                self.current_ship.engine_efficiency = self.ship_engine_efficiency_var.get()

            # Update hardpoints (generate simple hardpoint lists)
            if hasattr(self.current_ship, 'weapon_hardpoints'):
                weapon_count = self.ship_weapon_hardpoints_var.get()
                self.current_ship.weapon_hardpoints = self._generate_hardpoints('weapon', weapon_count)
            
            if hasattr(self.current_ship, 'turret_hardpoints'):
                turret_count = self.ship_turret_hardpoints_var.get()
                self.current_ship.turret_hardpoints = self._generate_hardpoints('turret', turret_count)
            
            if hasattr(self.current_ship, 'engine_hardpoints'):
                engine_count = self.ship_engine_hardpoints_var.get()
                self.current_ship.engine_hardpoints = self._generate_hardpoints('engine', engine_count)

            # Update visual assets
            if hasattr(self.current_ship, 'game_sprite'):
                self.current_ship.game_sprite = self.ship_game_sprite_var.get()
            if hasattr(self.current_ship, 'shipyard_sprite'):
                self.current_ship.shipyard_sprite = self.ship_shipyard_sprite_var.get()
            if hasattr(self.current_ship, 'animation_frames'):
                self.current_ship.animation_frames = self.ship_animation_frames_var.get()
            if hasattr(self.current_ship, 'sprite_size'):
                self.current_ship.sprite_size = self.ship_sprite_size_var.get()
            
            # Update animation settings
            if hasattr(self.current_ship, 'animation_type'):
                self.current_ship.animation_type = self.ship_anim_type_var.get()
            else:
                self.current_ship.animation_type = self.ship_anim_type_var.get()
            
            if hasattr(self.current_ship, 'frame_rate'):
                self.current_ship.frame_rate = self.ship_frame_rate_var.get()
            else:
                self.current_ship.frame_rate = self.ship_frame_rate_var.get()
                
            if hasattr(self.current_ship, 'idle_sequence'):
                self.current_ship.idle_sequence = self.ship_idle_sequence_var.get()
            else:
                self.current_ship.idle_sequence = self.ship_idle_sequence_var.get()
                
            if hasattr(self.current_ship, 'thrust_sequence'):
                self.current_ship.thrust_sequence = self.ship_thrust_sequence_var.get()
            else:
                self.current_ship.thrust_sequence = self.ship_thrust_sequence_var.get()

            # Update special features
            if hasattr(self.current_ship, 'ship_roles'):
                roles_text = self.ship_roles_var.get().strip()
                self.current_ship.ship_roles = [role.strip() for role in roles_text.split(',') if role.strip()]
            
            if hasattr(self.current_ship, 'special_abilities'):
                abilities_text = self.ship_special_abilities_var.get().strip()
                self.current_ship.special_abilities = [ability.strip() for ability in abilities_text.split(',') if ability.strip()]

            # Update requirements
            if hasattr(self.current_ship, 'required_reputation'):
                self.current_ship.required_reputation = self.ship_required_reputation_var.get()
            if hasattr(self.current_ship, 'hyperspace_speed'):
                self.current_ship.hyperspace_speed = self.ship_hyperspace_speed_var.get()

            # Update description and origin
            if hasattr(self.current_ship, 'description'):
                self.current_ship.description = self.ship_description_text.get(1.0, tk.END).strip()
            if hasattr(self.current_ship, 'origin'):
                self.current_ship.origin = self.ship_origin_var.get()

            messagebox.showinfo("Success", f"Saved ship: {self.current_ship.name}")
            self.load_ships()  # Refresh the list
            self.auto_save_ships()  # Auto-save changes

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save ship: {e}")

    def _generate_hardpoints(self, hardpoint_type, count):
        """Generate a list of hardpoints for the ship."""
        hardpoints = []
        for i in range(count):
            hardpoint = {
                'id': f"{hardpoint_type}_{i+1}",
                'type': hardpoint_type,
                'size': 'any',
                'arc': 360 if hardpoint_type == 'turret' else 45,
                'position': {'x': 0, 'y': 0}
            }
            hardpoints.append(hardpoint)
        return hardpoints

    def create_new_ship(self):
        """Create a new ship."""
        ship_id = simpledialog.askstring("New Ship", "Enter a unique ID for the new ship:")
        if not ship_id:
            return

        ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
        if ship_id in ships_source:
            messagebox.showerror("Error", f"A ship with ID '{ship_id}' already exists")
            return

        try:
            # Create a basic ship object
            if 'StandardizedShip' in globals():
                new_ship = StandardizedShip(ship_id, ship_id.replace('_', ' ').title(), "fighter", "small")
            else:
                # Create a simple object if classes aren't available
                class SimpleShip:
                    def __init__(self, id, name):
                        self.id = id
                        self.name = name
                        self.ship_class = "fighter"
                        self.size = "small"
                        self.cost = 10000
                        self.outfit_space = 20
                        self.cargo_space = 10
                        self.max_speed = 3.0
                        self.acceleration = 0.5
                        self.turn_rate = 1.0
                        self.shields = 50
                        self.armor = 30
                        self.min_tech_level = 1
                        self.description = ""

                new_ship = SimpleShip(ship_id, ship_id.replace('_', ' ').title())

            # Add to both registries
            SHIPS[ship_id] = new_ship
            if STANDARDIZED_SHIPS_REGISTRY:
                STANDARDIZED_SHIPS_REGISTRY[ship_id] = new_ship

            self.load_ships()
            messagebox.showinfo("Success", f"Created new ship: {new_ship.name}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create ship: {e}")

    def delete_ship(self):
        """Delete the selected ship."""
        selection = self.ship_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "No ship selected to delete")
            return

        # Get selected ship
        ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
        ships_list = list(ships_source.items())

        if selection[0] < len(ships_list):
            ship_id, ship = ships_list[selection[0]]

            result = messagebox.askyesno("Confirm Delete",
                                       f"Are you sure you want to delete '{ship.name}'?")
            if result:
                if ship_id in SHIPS:
                    del SHIPS[ship_id]
                if ship_id in STANDARDIZED_SHIPS_REGISTRY:
                    del STANDARDIZED_SHIPS_REGISTRY[ship_id]
                
                self.load_ships()
                messagebox.showinfo("Success", f"Deleted ship: {ship.name}")

    def export_ships(self):
        """Export ships to JSON file."""
        filename = filedialog.asksaveasfilename(
            title="Export Ships",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            try:
                ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
                ships_data = {}
                
                for ship_id, ship in ships_source.items():
                    ships_data[ship_id] = self._ship_to_dict(ship)

                with open(filename, 'w') as f:
                    json.dump(ships_data, f, indent=2)

                messagebox.showinfo("Success", f"Exported {len(ships_data)} ships to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to export ships: {e}")

    def save_all_ships(self):
        """Save all ships to a JSON file for persistence."""
        filename = filedialog.asksaveasfilename(
            title="Save All Ships",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialvalue="ships_data.json"
        )
        if filename:
            try:
                ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
                ships_data = {}
                
                for ship_id, ship in ships_source.items():
                    ships_data[ship_id] = self._ship_to_dict(ship)

                with open(filename, 'w') as f:
                    json.dump(ships_data, f, indent=2)

                messagebox.showinfo("Success", f"Saved {len(ships_data)} ships to {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to save ships: {e}")

    def auto_save_ships(self):
        """Automatically save ships to the default file."""
        try:
            ships_source = SHIPS if SHIPS else STANDARDIZED_SHIPS_REGISTRY
            ships_data = {}
            
            for ship_id, ship in ships_source.items():
                ships_data[ship_id] = self._ship_to_dict(ship)

            with open("ships_data.json", 'w') as f:
                json.dump(ships_data, f, indent=2)

            print(f"Auto-saved {len(ships_data)} ships to ships_data.json")

        except Exception as e:
            print(f"Auto-save ships failed: {e}")

    def _ship_to_dict(self, ship):
        """Convert a ship object to a dictionary for JSON export."""
        return {
            'id': getattr(ship, 'id', ship.name.lower().replace(' ', '_')),
            'name': ship.name,
            'ship_class': getattr(ship, 'ship_class', 'fighter'),
            'size': getattr(ship, 'size', 'small'),
            'cost': getattr(ship, 'cost', 10000),
            'outfit_space': getattr(ship, 'outfit_space', 20),
            'cargo_space': getattr(ship, 'cargo_space', 10),
            'mass': getattr(ship, 'mass', 1.0),
            'min_tech_level': getattr(ship, 'min_tech_level', 1),
            'max_speed': getattr(ship, 'max_speed', 3.0),
            'acceleration': getattr(ship, 'acceleration', 0.5),
            'turn_rate': getattr(ship, 'turn_rate', 1.0),
            'shields': getattr(ship, 'shields', 50),
            'armor': getattr(ship, 'armor', 30),
            'shield_recharge_rate': getattr(ship, 'shield_recharge_rate', 1.0),
            'shipyard_sprite': getattr(ship, 'shipyard_sprite', ''),
            'game_sprite': getattr(ship, 'game_sprite', ''),
            'animation_frames': getattr(ship, 'animation_frames', 1),
            'sprite_size': getattr(ship, 'sprite_size', 32),
            'animation_type': getattr(ship, 'animation_type', 'rotation'),
            'frame_rate': getattr(ship, 'frame_rate', 30),
            'idle_sequence': getattr(ship, 'idle_sequence', '0-0'),
            'thrust_sequence': getattr(ship, 'thrust_sequence', '0-0'),
            'weapon_hardpoints': getattr(ship, 'weapon_hardpoints', []),
            'turret_hardpoints': getattr(ship, 'turret_hardpoints', []),
            'engine_hardpoints': getattr(ship, 'engine_hardpoints', []),
            'fuel_capacity': getattr(ship, 'fuel_capacity', 100),
            'energy_capacity': getattr(ship, 'energy_capacity', 100),
            'energy_recharge': getattr(ship, 'energy_recharge', 10.0),
            'crew_capacity': getattr(ship, 'crew_capacity', 1),
            'passenger_capacity': getattr(ship, 'passenger_capacity', 0),
            'required_reputation': getattr(ship, 'required_reputation', 0),
            'required_missions': getattr(ship, 'required_missions', []),
            'availability_systems': getattr(ship, 'availability_systems', []),
            'maneuverability_class': getattr(ship, 'maneuverability_class', 'medium'),
            'engine_efficiency': getattr(ship, 'engine_efficiency', 1.0),
            'hyperspace_speed': getattr(ship, 'hyperspace_speed', 1.0),
            'special_abilities': getattr(ship, 'special_abilities', []),
            'ship_roles': getattr(ship, 'ship_roles', ['general']),
            'description': getattr(ship, 'description', ''),
            'manufacturer': getattr(ship, 'manufacturer', 'Unknown'),
            'origin': getattr(ship, 'origin', 'Unknown'),
            'default_outfits': getattr(ship, 'default_outfits', {})
        }
        
    def preview_ship_animation(self):
        """Preview the ship animation settings."""
        sprite_path = self.ship_game_sprite_var.get()
        if not sprite_path:
            messagebox.showwarning("Warning", "No game sprite path specified")
            return
            
        if not os.path.exists(sprite_path):
            messagebox.showerror("Error", f"Sprite file not found: {sprite_path}")
            return
            
        # Show animation preview dialog
        anim_type = self.ship_anim_type_var.get()
        frame_rate = self.ship_frame_rate_var.get()
        idle_seq = self.ship_idle_sequence_var.get()
        thrust_seq = self.ship_thrust_sequence_var.get()
        
        preview_info = f"Animation Preview:\n\n"
        preview_info += f"Sprite: {sprite_path}\n"
        preview_info += f"Type: {anim_type}\n"
        preview_info += f"Frame Rate: {frame_rate} FPS\n"
        preview_info += f"Idle Sequence: {idle_seq}\n"
        preview_info += f"Thrust Sequence: {thrust_seq}\n\n"
        preview_info += f"Note: Full animation preview requires the game engine.\n"
        preview_info += f"Save the ship and load it in-game to see the animation."
        
        messagebox.showinfo("Animation Preview", preview_info)

def main():
    """Main function to run the enhanced editor."""
    root = tk.Tk()
    app = EnhancedEditor(root)
    root.mainloop()

if __name__ == "__main__":
    main()
