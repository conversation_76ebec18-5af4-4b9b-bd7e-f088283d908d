"""
OUTFIT RULES ENGINE - COMPREHENSIVE GAME MECHANICS SYSTEM
=========================================================

This document outlines the complete outfit rules engine that defines how different 
outfit categories work in Escape Velocity Py. The system separates RULES (how things work) 
from CONTENT (specific items), allowing the outfit editor to create items that 
follow these established physics.

=============================================================================
SYSTEM ARCHITECTURE
=============================================================================

1. **RULES ENGINE** (outfit_rules_engine.py)
   - Defines the physics and mechanics of how outfits work
   - Handles system interactions, power consumption, timing
   - Provides update loops for each outfit category

2. **OUTFIT DEFINITIONS** (standardized_outfits.py)
   - Base classes with all properties needed by rules engine
   - Extensible property system for future expansion
   - Type-safe outfit creation and management

3. **CONTENT CREATION** (example_outfits.py)
   - Specific outfit instances using the rules framework
   - Editor-friendly outfit creation
   - Balanced gameplay progression

=============================================================================
DEFENSE SYSTEM RULES
=============================================================================

MOUNT TYPES:
- **PASSIVE**: Always active (shields, armor, ECM)
- **FIXED**: Forward-facing point defense with limited arc
- **TURRET**: 360° rotating point defense systems
- **OMNIDIRECTIONAL**: Full coverage (reactive systems)

ACTIVATION TYPES:
- **AUTOMATIC**: Fires automatically at detected threats
- **MANUAL**: Player-controlled activation
- **TRIGGERED**: Activates on damage/proximity events

TARGET TYPES:
- **PROJECTILES**: Anti-missile/torpedo systems
- **SHIPS**: Anti-ship defensive weapons
- **ENERGY**: Energy weapon deflection/absorption
- **ALL**: Universal defense systems

DEFENSE MECHANICS:
- Point defense systems automatically engage incoming projectiles
- Accuracy calculated based on: distance, target speed, electronics bonuses
- Power consumption per shot with cooldown timers
- ECM systems jam enemy targeting and sensors
- Reactive armor provides damage reduction and special effects

EXAMPLE DEFENSE OUTFITS:
- Point Defense Turret: Automated missile interception
- Anti-Missile Gun: Fixed-arc high-accuracy defense  
- Flak Cannon: Area-effect anti-projectile system
- ECM Suite: Electronic jamming and countermeasures
- Reactive Armor: Damage-triggered defensive response

=============================================================================
ENGINE SYSTEM RULES
=============================================================================

ENGINE TYPES:
- **MAIN**: Primary propulsion systems
- **MANEUVERING**: RCS thrusters for rotation/positioning
- **AFTERBURNER**: Temporary high-performance boost
- **JUMP**: Hyperspace drive systems

ENGINE STATES:
- **OFF**: No power consumption, no functionality
- **IDLE**: Minimal power drain, ready for activation
- **ACTIVE**: Full operation with power/fuel consumption
- **OVERLOAD**: Excessive heat causing reduced performance

ENGINE MECHANICS:
- Forward/reverse thrust with power and fuel consumption
- Turn rate and acceleration modifications
- Afterburner systems with high power/fuel costs
- Heat generation and cooling systems
- Fuel efficiency calculations
- Thrust vectoring for advanced maneuvering

REVERSE THRUST SYSTEM:
- Basic ships have NO reverse capability
- Reverse Thrusters: Basic reverse functionality
- Maneuvering Thrusters: Enhanced omnidirectional control
- Advanced Engines: Full thrust vectoring capability

ENGINE PROGRESSION:
1. **Starter Ships**: Turn-and-burn physics only
2. **Early Game**: Basic reverse thrusters available
3. **Mid Game**: Enhanced maneuvering systems
4. **Late Game**: Advanced omnidirectional control

=============================================================================
ELECTRONICS SYSTEM RULES
=============================================================================

ELECTRONICS TYPES:
- **SENSORS**: Detection and classification systems
- **TARGETING**: Weapon accuracy and lock systems
- **ECM/ECCM**: Electronic warfare capabilities
- **COMMUNICATION**: Long