#!/usr/bin/env python3
"""
Simple test to check sprite dimensions and fix sizing issues
"""

import pygame as pg
import os
import json

def test_sprite_loading():
    # Initialize pygame
    pg.init()
    screen = pg.display.set_mode((100, 100))  # Minimal window
    
    # Test loading the scout spritesheet directly
    sprite_path = "assets/images/sprites/ships/small/scout_spritesheet.png"
    metadata_path = "assets/images/sprites/ships/small/scout_metadata.json"
    
    print("Testing direct sprite loading...")
    
    if os.path.exists(sprite_path):
        try:
            # Load the image
            image = pg.image.load(sprite_path).convert_alpha()
            print(f"✓ Loaded sprite: {sprite_path}")
            print(f"  Actual image dimensions: {image.get_size()}")
            
            # Load metadata
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                print(f"  Metadata sprite_size: {metadata['sprite_size']}")
                print(f"  Metadata sheet dimensions: {metadata['sheet_width']}x{metadata['sheet_height']}")
                print(f"  Metadata frame_count: {metadata['frame_count']}")
                
                # Calculate expected dimensions
                expected_width = metadata['frame_count'] * metadata['sprite_size']
                expected_height = metadata['sprite_size']
                print(f"  Expected dimensions: {expected_width}x{expected_height}")
                
                # Check if they match
                actual_width, actual_height = image.get_size()
                if actual_width == expected_width and actual_height == expected_height:
                    print("  ✓ Dimensions match metadata")
                else:
                    print(f"  ✗ Dimension mismatch! Actual: {actual_width}x{actual_height}, Expected: {expected_width}x{expected_height}")
                
                # Test extracting a frame
                sprite_size = metadata['sprite_size']
                frame_surface = pg.Surface((sprite_size, sprite_size), pg.SRCALPHA)
                frame_surface.blit(image, (0, 0), (0, 0, sprite_size, sprite_size))
                print(f"  ✓ Successfully extracted frame 0 with size: {frame_surface.get_size()}")
                
            else:
                print(f"  ✗ Metadata not found: {metadata_path}")
                
        except Exception as e:
            print(f"  ✗ Error loading sprite: {e}")
    else:
        print(f"✗ Sprite not found: {sprite_path}")
    
    pg.quit()

if __name__ == "__main__":
    test_sprite_loading()
