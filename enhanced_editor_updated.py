"""
Escape Velocity Py - Enhanced Content Editor - RULES ENGINE VERSION
Updated to work with the comprehensive outfit rules engine system
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import os
import sys
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

try:
    from game_objects.standardized_outfits import *
    from game_objects.example_outfits import *
    from game_objects.ships import *
    from game_objects.standardized_ships import *
    print("Successfully imported outfit and ship systems")
    print(f"Found {len(OUTFITS_REGISTRY)} outfits in registry")
    print(f"Found {len(SHIPS)} ships in registry")
    print(f"Found {len(STANDARDIZED_SHIPS_REGISTRY)} standardized ships in registry")
except ImportError as e:
    print(f"Error importing game systems: {e}")
    print("Make sure the game files are in the correct location")
    # Create empty registries to prevent crashes
    OUTFITS_REGISTRY = {}
    SHIPS = {}
    STANDARDIZED_SHIPS_REGISTRY = {}

class EnhancedEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("EV Py - Enhanced Content Editor - Rules Engine Version")
        self.root.geometry("1600x1000")

        # Current selections
        self.current_outfit = None
        self.current_ship = None

        # Define constants from rules engine
        self.behavior_types = ["instant", "dumbfire", "guided", "beam", "delayed", "proximity", "mine"]
        self.mount_types = ["fixed", "turret", "guided", "bomber"]
        self.damage_types = ["energy", "kinetic", "explosive", "emp", "ion"]
        self.ammo_types = ["light_missile", "heavy_missile", "torpedo", "rocket", "mine"]
        self.ship_sizes = ["small", "medium", "large", "capital"]
        
        # Defense mount types from rules engine
        self.defense_mount_types = ["passive", "fixed", "turret", "omni"]
        self.defense_target_types = ["projectiles", "ships", "energy", "all"]
        self.defense_activation_types = ["automatic", "manual", "triggered"]
        
        # Engine types from rules engine
        self.engine_types = ["main", "maneuvering", "afterburner", "jump"]
        self.engine_states = ["off", "idle", "active", "overload"]
        self.thrust_directions = ["forward", "reverse", "omni"]
        
        # Electronics types from rules engine
        self.electronics_types = ["sensors", "targeting", "ecm", "communication", "navigation", "scanning"]
        self.electronics_states = ["off", "passive", "active", "jammed"]

        self.setup_ui()
        self.load_data