#!/usr/bin/env python3
"""
Test script to verify ship loading system works correctly
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ship_loading():
    print("Testing ship loading system...")
    
    try:
        # Import the ship system
        from game_objects.ships import SHIPS, get_ship_by_id
        print(f"✓ Successfully imported SHIPS dict with {len(SHIPS)} ships")
        
        # Test getting a specific ship
        scout = get_ship_by_id('scout')
        if scout:
            print(f"✓ Scout ship found: {scout.name} ({scout.ship_class}, {scout.size})")
            print(f"  - Outfit space: {scout.outfit_space}")
            print(f"  - Cargo space: {scout.cargo_space}")
            print(f"  - Max speed: {scout.max_speed}")
        else:
            print("✗ Scout ship not found")
            
        # List all available ships
        print(f"\nAvailable ships ({len(SHIPS)}):")
        for ship_id, ship in SHIPS.items():
            print(f"  - {ship_id}: {ship.name} ({ship.ship_class}, {ship.size})")
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing ship system: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ship_loading()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
