"""
Escape Velocity Py - Enhanced Content Editor - WORKING VERSION
This is a properly structured version with all the fixes applied.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import json
import os
import sys
from pathlib import Path

# Add the game's src directory to the path
game_src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(game_src_path))

try:
    from game_objects.standardized_outfits import *
    from game_objects.example_outfits import *
    from game_objects.ships import *
    from game_objects.standardized_ships import *
    print("Successfully imported outfit and ship systems")
    print(f"Found {len(OUTFITS_REGISTRY)} outfits in registry")
    print(f"Found {len(SHIPS)} ships in registry")
    print(f"Found {len(STANDARDIZED_SHIPS_REGISTRY)} standardized ships in registry")
except ImportError as e:
    print(f"Error importing game systems: {e}")
    print("Make sure the game files are in the correct location")
    # Create empty registries to prevent crashes
    OUTFITS_REGISTRY = {}
    SHIPS = {}
    STANDARDIZED_SHIPS_REGISTRY = {}

class EnhancedEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("EV Py - Enhanced Content Editor - WORKING")
        self.root.geometry("1400x900")

        # Current selections
        self.current_outfit = None
        self.current_ship = None

        # Define behavior constants in case imports fail
        self.behavior_types = ["instant", "dumbfire", "guided", "beam", "delayed", "proximity"]
        self.mount_types = ["fixed", "turret"]
        self.damage_types = ["energy", "kinetic", "explosive"]
        self.ammo_types = ["light_missile", "heavy_missile", "torpedo", "rocket"]
        self.ship_sizes = ["small", "medium", "large", "capital"]

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Setup comprehensive UI with all tabs."""
        # Create notebook for categories
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create all tabs
        self.create_weapons_tab()
        self.create_ammunition_tab()
        self.create_defense_tab()
        self.create_engines_tab()
        self.create_electronics_tab()
        self.create_utility_tab()
        self.create_ships_tab()

    def load_data(self):
        """Load all outfit and ship data."""
        print("Loading outfit and ship data...")
        self.load_saved_outfits()  # Try to load saved data first
        self.load_outfits()
        self.load_ships()

    def load_saved_outfits(self):
        """Load previously saved outfit data if available."""
        save_file = "outfits_data.json"
        if os.path.exists(save_file):
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing outfits with saved data
                for outfit_id, outfit_data in saved_data.items():
                    if outfit_id in OUTFITS_REGISTRY:
                        outfit = OUTFITS_REGISTRY[outfit_id]
                        # Update basic properties
                        for prop in ['name', 'cost', 'space_required', 'min_tech_level', 'outfitter_icon', 'description']:
                            if prop in outfit_data and hasattr(outfit, prop):
                                setattr(outfit, prop, outfit_data[prop])

                        # Update category-specific properties
                        if outfit.category == "weapons":
                            for prop in ['mount_type', 'damage', 'fire_rate', 'range', 'energy_usage', 'uses_ammo', 'ammo_type', 'max_ammo']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])
                        elif outfit.category == "ammunition":
                            for prop in ['ammo_type', 'quantity', 'damage', 'projectile_speed', 'projectile_behavior', 'tracking_strength', 'explosion_radius']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])
                        elif outfit.category == "defense":
                            for prop in ['shield_boost', 'armor_boost', 'shield_recharge_boost', 'damage_reduction']:
                                if prop in outfit_data and hasattr(outfit, prop):
                                    setattr(outfit, prop, outfit_data[prop])

                print(f"Loaded saved outfit data from {save_file}")

            except Exception as e:
                print(f"Failed to load saved outfit data: {e}")

    def load_outfits(self):
        """Load outfit data."""
        print(f"Found {len(OUTFITS_REGISTRY)} outfits in registry")
        self.load_weapons()
        self.load_ammunition()
        self.load_defense()
        self.load_engines()
        self.load_electronics()
        self.load_utility()

    def load_ships(self):
        """Load ship data."""
        print(f"Found {len(SHIPS)} ships in registry")
        if hasattr(self, 'ship_listbox'):
            self.ship_listbox.delete(0, tk.END)
            for ship_id, ship in SHIPS.items():
                display_name = f"{ship.name} ({ship.size})"
                self.ship_listbox.insert(tk.END, display_name)

    def load_weapons(self):
        """Load weapons into the listbox."""
        if hasattr(self, 'weapon_listbox'):
            self.weapon_listbox.delete(0, tk.END)

            for outfit_id, outfit in OUTFITS_REGISTRY.items():
                #