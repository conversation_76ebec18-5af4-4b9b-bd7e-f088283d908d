# ⚡🚫 COMPLETE POWER BLACKOUT SYSTEM IMPLEMENTED! ⚡🚫

## 🔌 **Total System Shutdown at Zero Power**

When ship power reaches 0, **ALL SYSTEMS GO OFFLINE**:

### **🚫 Disabled Systems:**
- **Movement**: No thrust (forward/backward completely disabled)
- **Turning**: No maneuvering (ship becomes a drifting hulk)
- **Weapons**: All weapon systems offline
- **Shield Regeneration**: Shields cannot recharge without power

### **⚡ Power Recovery:**
- **Automatic Regeneration**: Power slowly regenerates (3-6/sec by ship size)
- **Gradual System Restoration**: As power returns, systems come back online
- **Emergency Operation**: At very low power (1-10%), minimal systems work at 10% capacity

## 🏥 **Planet Docking = Full Restoration**

### **Power Station Lore:**
When docking at any planet, the ship connects to the station's power grid:
- **100% Power Restoration**: Instant full reactor charge
- **100% Fuel Restoration**: Complete fuel tanks refilled
- **Welcome Message**: "Power & Fuel restored! Welcome to [Planet Name]"

### **Strategic Implications:**
- **Emergency Docking**: Players with dead ships can drift to planets for rescue
- **Resource Management**: Planets become critical refueling stations
- **Exploration Risk**: Venturing too far from planets becomes dangerous

## 🎮 **Gameplay Mechanics**

### **Power Failure Progression:**
1. **Normal Operation** (30%+ power): All systems work normally
2. **Low Power Warning** (10-30% power): Yellow power bar, systems work normally
3. **Critical Power** (1-10% power): Red power bar, "CRITICAL POWER" warning
   - Turning at 10% normal rate
   - Thrust at 10% normal power
   - Weapons still function but drain power quickly
4. **Complete Blackout** (0% power): Red "[OFFLINE]" indicator
   - **No movement whatsoever**
   - **No turning capability** 
   - **No weapons**
   - **No shield regeneration**
   - Occasional "POWER FAILURE - All systems offline!" message

### **Power Consumption Costs:**
- **Turning**: 1 power per second (small cost)
- **Forward Thrust**: 5 power per second
- **Reverse Thrust**: 2.5 power per second  
- **Weapons**: 10+ power per shot
- **Shield Regen**: 2 power per second

## 🖥️ **UI Indicators**

### **Power Status Display:**
```
Power: 0/100 [OFFLINE]     ← Zero power indicator
>> ALL SYSTEMS OFFLINE << ← Warning when power = 0
>> CRITICAL POWER <<       ← Warning when power < 10%
```

### **Color Coding:**
- **Green**: >30% power (safe operation)
- **Yellow**: 10-30% power (caution)
- **Red**: <10% power (critical/offline)

### **Status Messages:**
- **"POWER FAILURE - All systems offline!"** (0% power)
- **"Critical power - minimal thrust!"** (very low power)
- **"No power for weapons!"** (weapon attempts at 0% power)
- **"Docking: Power & Fuel restored!"** (planet docking)

## ⚙️ **Technical Implementation**

### **Complete Blackout Logic:**
```python
if self.ship.power <= 0:
    # COMPLETE BLACKOUT - no movement at all
    acc = pg.math.Vector2(0, 0)  # No thrust
    # No turning allowed
    # Weapons disabled
    # Shield regen stopped
```

### **Emergency Power Logic:**
```python
elif self.ship.power > 0:
    # Minimal turning with remaining power
    self.angle += self.turn_rate * 0.1  # 10% turn rate
    acc = forward * thrust_strength * 0.1  # 10% thrust
```

### **Planet Restoration:**
```python
# Full power/fuel restoration on docking
self.player.ship.power = self.player.ship.power_capacity
self.player.ship.fuel = self.player.ship.fuel_capacity
```

## 🎯 **Strategic Gameplay Impact**

### **Risk vs Reward:**
- **Aggressive Combat**: Rapid firing drains power quickly
- **Exploration**: Long journeys risk power depletion
- **Emergency Planning**: Always plan routes near planets

### **Power Management Strategy:**
- **Conservation**: Don't waste power on unnecessary movement
- **Combat Timing**: Choose when to engage based on power levels
- **Emergency Protocols**: Know where nearest planets are
- **Loadout Planning**: Higher power capacity ships for long missions

### **Rescue Scenarios:**
- **Dead Ship Drift**: Players can drift on momentum to reach planets
- **Towing**: Other ships could theoretically push/guide dead ships
- **Emergency Beacons**: Future feature - call for rescue

## 🚀 **Foundation for Advanced Systems**

This complete power system enables future features:

### **Outfit Possibilities:**
- **Emergency Power Cells**: Backup power when main reactor fails
- **Efficient Systems**: Reduced power consumption
- **Quick-Charge Reactors**: Faster power regeneration
- **Solar Panels**: Slow power regen in space (no planet needed)

### **Emergency Equipment:**
- **Backup Thrusters**: Minimal movement capability at zero power
- **Emergency Beacon**: Distress signal when power critical
- **Power Transfer**: Share power between ships

### **Advanced Mechanics:**
- **System Prioritization**: Choose which systems get power first
- **Power Routing**: Redirect power from non-essential systems
- **Reactor Damage**: Power capacity reduced by combat damage

## 🎮 **Test the Complete System**

1. **Power Drain Test**: Fire weapons rapidly until power hits zero
2. **Blackout Experience**: Try to move/turn/shoot with 0% power
3. **Emergency Drift**: Use momentum to reach a planet while powerless
4. **Planet Rescue**: Dock at planet and watch full restoration
5. **Critical Power**: Experience 1-10% power emergency operation

The complete power blackout system transforms ship management into a critical survival mechanic! ⚡🚫🎮