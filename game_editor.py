"""
Escape Velocity Py - Game Editor

A comprehensive editor for managing game objects (ships, planets, stations, outfits, etc.)
in the Escape Velocity Py game.
"""

import os
import sys
import json
import importlib.util
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
from tkinter.colorchooser import askcolor
import pygame as pg
from PIL import Image, ImageTk
import shutil
import inspect

# Add the src directory to the path so we can import game modules
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import game modules
try:
    from game_objects.ships import Ship, SHIPS, get_ship_by_id, SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL
    from game_objects.ships import CLASS_FIGHTER, CLASS_FREIGHTER, CLASS_TRANSPORT, CLASS_CORVETTE, CLASS_FRIGATE
    from game_objects.ships import CLASS_CRUISER, CLASS_DESTROYER, CLASS_CARRIER, CLASS_BATTLESHIP, CLASS_DREADNOUGHT
    from game_objects.planet import Planet, PLANET_TYPES, TECH_LEVEL_MIN, TECH_LEVEL_MAX
    from game_objects.outfits import Outfit, OUTFITS, get_outfit_by_id
    from game_objects.sprite_manager import SpriteSheet, load_sprite
except ImportError as e:
    print(f"Error importing game modules: {e}")
    print("Make sure you're running this from the EscapeVelocityPy directory.")
    sys.exit(1)

# Constants
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
PREVIEW_SIZE = 256
LISTBOX_WIDTH = 30
PROPERTY_WIDTH = 40

class GameEditor(tk.Tk):
    """Main editor application."""

    def __init__(self):
        super().__init__()

        self.title("Escape Velocity Py - Game Editor")
        self.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.minsize(800, 600)

        # Initialize pygame for sprite handling
        pg.init()

        # Create the main notebook (tabbed interface)
        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create tabs for different object types
        self.ship_editor = ShipEditor(self.notebook)
        self.planet_editor = PlanetEditor(self.notebook)
        self.outfit_editor = OutfitEditor(self.notebook)
        self.station_editor = StationEditor(self.notebook)

        self.notebook.add(self.ship_editor, text="Ships")
        self.notebook.add(self.planet_editor, text="Planets")
        self.notebook.add(self.outfit_editor, text="Outfits")
        self.notebook.add(self.station_editor, text="Stations")

        # Create the menu bar
        self.create_menu()

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind events
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

        # Load initial data
        self.load_all_data()

    def create_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Save All Changes", command=self.save_all_data)
        file_menu.add_command(label="Reload All Data", command=self.load_all_data)
        file_menu.add_separator()
        file_menu.add_command(label="Import Spritesheet", command=self.import_spritesheet)
        file_menu.add_command(label="Launch Spritesheet Generator", command=self.launch_spritesheet_generator)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.quit)

        menubar.add_cascade(label="File", menu=file_menu)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        tools_menu.add_command(label="Validate Game Data", command=self.validate_game_data)
        tools_menu.add_command(label="Generate Sample Objects", command=self.generate_sample_objects)

        menubar.add_cascade(label="Tools", menu=tools_menu)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="Help", command=self.show_help)

        menubar.add_cascade(label="Help", menu=help_menu)

        self.config(menu=menubar)

    def on_tab_changed(self, event):
        """Handle tab change events."""
        current_tab = self.notebook.select()
        tab_text = self.notebook.tab(current_tab, "text")
        self.status_var.set(f"Editing {tab_text}")

    def load_all_data(self):
        """Load all game data."""
        try:
            self.ship_editor.load_data()
            self.planet_editor.load_data()
            self.outfit_editor.load_data()
            self.station_editor.load_data()
            self.status_var.set("All data loaded successfully")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load game data: {str(e)}")
            self.status_var.set("Error loading data")

    def save_all_data(self):
        """Save all game data."""
        try:
            self.ship_editor.save_data()
            self.planet_editor.save_data()
            self.outfit_editor.save_data()
            self.station_editor.save_data()
            self.status_var.set("All data saved successfully")
            messagebox.showinfo("Success", "All game data has been saved successfully.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save game data: {str(e)}")
            self.status_var.set("Error saving data")

    def import_spritesheet(self):
        """Import a spritesheet into the game."""
        # Get the current tab to determine the object type
        current_tab = self.notebook.select()
        tab_text = self.notebook.tab(current_tab, "text").lower()

        # Ask for the spritesheet file
        file_path = filedialog.askopenfilename(
            title="Select Spritesheet",
            filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
        )

        if not file_path:
            return

        # Ask for metadata file (optional)
        metadata_path = filedialog.askopenfilename(
            title="Select Metadata (Optional)",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            initialdir=os.path.dirname(file_path)
        )

        # Determine the destination directory based on the tab
        if tab_text == "ships":
            # Ask for ship size
            size = simpledialog.askstring(
                "Ship Size",
                "Enter ship size (small, medium, large, capital):",
                initialvalue="small"
            )
            if size not in ["small", "medium", "large", "capital"]:
                messagebox.showerror("Error", "Invalid ship size")
                return
            dest_dir = os.path.join("assets", "images", "sprites", "ships", size)
        elif tab_text == "planets":
            dest_dir = os.path.join("assets", "images", "sprites", "planets")
        elif tab_text == "stations":
            dest_dir = os.path.join("assets", "images", "sprites", "stations")
        elif tab_text == "outfits":
            dest_dir = os.path.join("assets", "images", "sprites", "outfits")
        else:
            dest_dir = os.path.join("assets", "images", "sprites")

        # Create the directory if it doesn't exist
        os.makedirs(dest_dir, exist_ok=True)

        # Copy the files to the destination
        try:
            shutil.copy2(file_path, dest_dir)
            if metadata_path:
                shutil.copy2(metadata_path, dest_dir)

            self.status_var.set(f"Imported spritesheet to {dest_dir}")
            messagebox.showinfo("Success", f"Spritesheet imported successfully to {dest_dir}")

            # Refresh the current editor
            if tab_text == "ships":
                self.ship_editor.load_data()
            elif tab_text == "planets":
                self.planet_editor.load_data()
            elif tab_text == "stations":
                self.station_editor.load_data()
            elif tab_text == "outfits":
                self.outfit_editor.load_data()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to import spritesheet: {str(e)}")

    def launch_spritesheet_generator(self):
        """Launch the spritesheet generator."""
        generator_path = os.path.join(os.path.dirname(__file__), "generate_sprite_sheet_improved.py")

        if not os.path.exists(generator_path):
            messagebox.showerror("Error", "Spritesheet generator not found")
            return

        try:
            # Use importlib to run the generator
            spec = importlib.util.spec_from_file_location("generator", generator_path)
            generator = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(generator)

            self.status_var.set("Launched spritesheet generator")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch spritesheet generator: {str(e)}")

    def validate_game_data(self):
        """Validate all game data for consistency."""
        # This would check for missing references, invalid values, etc.
        messagebox.showinfo("Validation", "Game data validation not yet implemented")

    def generate_sample_objects(self):
        """Generate sample game objects for testing."""
        messagebox.showinfo("Sample Objects", "Sample object generation not yet implemented")

    def show_about(self):
        """Show the about dialog."""
        about_text = """
        Escape Velocity Py - Game Editor

        A comprehensive editor for managing game objects
        in the Escape Velocity Py game.

        Created for easy modification of game content
        without editing the game code directly.
        """
        messagebox.showinfo("About", about_text)

    def show_help(self):
        """Show the help dialog."""
        help_text = """
        Game Editor Help

        - Use the tabs to switch between different object types
        - Select an object from the list to edit its properties
        - Click 'New' to create a new object
        - Click 'Delete' to remove the selected object
        - Click 'Save' to save your changes

        For importing spritesheets:
        1. Go to File > Import Spritesheet
        2. Select the PNG file
        3. Optionally select a metadata JSON file

        For generating spritesheets:
        1. Go to File > Launch Spritesheet Generator
        2. Follow the instructions in the generator
        """
        messagebox.showinfo("Help", help_text)


class EditorTab(ttk.Frame):
    """Base class for editor tabs."""

    def __init__(self, parent):
        super().__init__(parent)

        # Create the main layout
        self.create_layout()

        # Preview image
        self.preview_image = None

    def create_layout(self):
        """Create the basic layout for the editor tab."""
        # Left panel - Object list
        left_frame = ttk.LabelFrame(self, text="Objects")
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=5)

        # Object list with scrollbar
        self.listbox_frame = ttk.Frame(left_frame)
        self.listbox_frame.pack(fill=tk.BOTH, expand=True)

        self.listbox = tk.Listbox(self.listbox_frame, width=LISTBOX_WIDTH, exportselection=0)
        self.listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        listbox_scrollbar = ttk.Scrollbar(self.listbox_frame, orient=tk.VERTICAL, command=self.listbox.yview)
        listbox_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.listbox.config(yscrollcommand=listbox_scrollbar.set)

        # Buttons for list operations
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(button_frame, text="New", command=self.create_new).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Delete", command=self.delete_selected).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Duplicate", command=self.duplicate_selected).pack(side=tk.LEFT, padx=2)

        # Right panel - Properties and preview
        right_frame = ttk.Frame(self)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Preview panel
        preview_frame = ttk.LabelFrame(right_frame, text="Preview")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.preview_canvas = tk.Canvas(preview_frame, width=PREVIEW_SIZE, height=PREVIEW_SIZE, bg="black")
        self.preview_canvas.pack(side=tk.TOP, padx=10, pady=10)

        preview_controls = ttk.Frame(preview_frame)
        preview_controls.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(preview_controls, text="Load Sprite", command=self.load_sprite).pack(side=tk.LEFT, padx=2)
        ttk.Button(preview_controls, text="Generate Sprite", command=self.generate_sprite).pack(side=tk.LEFT, padx=2)

        # Properties panel
        properties_frame = ttk.LabelFrame(right_frame, text="Properties")
        properties_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Scrollable properties
        self.properties_canvas = tk.Canvas(properties_frame)
        self.properties_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        properties_scrollbar = ttk.Scrollbar(properties_frame, orient=tk.VERTICAL, command=self.properties_canvas.yview)
        properties_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.properties_canvas.configure(yscrollcommand=properties_scrollbar.set)
        self.properties_canvas.bind('<Configure>', lambda e: self.properties_canvas.configure(scrollregion=self.properties_canvas.bbox("all")))

        self.properties_frame = ttk.Frame(self.properties_canvas)
        self.properties_canvas.create_window((0, 0), window=self.properties_frame, anchor="nw")

        # Save button
        ttk.Button(right_frame, text="Save Changes", command=self.save_data).pack(side=tk.BOTTOM, pady=5)

        # Bind events
        self.listbox.bind('<<ListboxSelect>>', self.on_select)

    def load_data(self):
        """Load data for this tab. To be implemented by subclasses."""
        pass

    def save_data(self):
        """Save data for this tab. To be implemented by subclasses."""
        pass

    def on_select(self, event):
        """Handle selection in the listbox."""
        if not self.listbox.curselection():
            return

        index = self.listbox.curselection()[0]
        selected_id = self.listbox.get(index)

        # Clear the properties frame
        for widget in self.properties_frame.winfo_children():
            widget.destroy()

        # Load the selected object's properties
        self.load_properties(selected_id)

        # Update the preview
        self.update_preview(selected_id)

    def load_properties(self, object_id):
        """Load properties for the selected object. To be implemented by subclasses."""
        pass

    def update_preview(self, object_id):
        """Update the preview for the selected object. To be implemented by subclasses."""
        pass

    def create_new(self):
        """Create a new object. To be implemented by subclasses."""
        pass

    def delete_selected(self):
        """Delete the selected object. To be implemented by subclasses."""
        if not self.listbox.curselection():
            messagebox.showwarning("Warning", "No object selected")
            return

        index = self.listbox.curselection()[0]
        selected_id = self.listbox.get(index)

        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{selected_id}'?"):
            self.delete_object(selected_id)

    def delete_object(self, object_id):
        """Delete an object. To be implemented by subclasses."""
        pass

    def duplicate_selected(self):
        """Duplicate the selected object. To be implemented by subclasses."""
        if not self.listbox.curselection():
            messagebox.showwarning("Warning", "No object selected")
            return

        index = self.listbox.curselection()[0]
        selected_id = self.listbox.get(index)

        self.duplicate_object(selected_id)

    def duplicate_object(self, object_id):
        """Duplicate an object. To be implemented by subclasses."""
        pass

    def load_sprite(self):
        """Load a sprite for the selected object."""
        if not self.listbox.curselection():
            messagebox.showwarning("Warning", "No object selected")
            return

        index = self.listbox.curselection()[0]
        selected_id = self.listbox.get(index)

        file_path = filedialog.askopenfilename(
            title="Select Sprite Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.bmp"), ("All files", "*.*")]
        )

        if file_path:
            self.load_sprite_for_object(selected_id, file_path)

    def load_sprite_for_object(self, object_id, file_path):
        """Load a sprite for an object. To be implemented by subclasses."""
        pass

    def generate_sprite(self):
        """Generate a sprite for the selected object."""
        if not self.listbox.curselection():
            messagebox.showwarning("Warning", "No object selected")
            return

        # This would launch the sprite generator with the selected object's info
        messagebox.showinfo("Generate Sprite", "Sprite generation not yet implemented")


class ShipEditor(EditorTab):
    """Editor for ship objects."""

    def __init__(self, parent):
        super().__init__(parent)
        self.ships = {}
        self.ship_properties = {}

    def load_data(self):
        """Load ship data."""
        self.ships = SHIPS.copy()

        # Clear the listbox
        self.listbox.delete(0, tk.END)

        # Add ships to the listbox
        for ship_id in sorted(self.ships.keys()):
            self.listbox.insert(tk.END, ship_id)

    def save_data(self):
        """Save ship data."""
        # This would write the updated ship data back to the ships.py file
        # For now, we'll just show a message
        messagebox.showinfo("Save Ships", "Ship data saving not yet implemented")

    def load_properties(self, ship_id):
        """Load properties for the selected ship."""
        if ship_id not in self.ships:
            return

        ship = self.ships[ship_id]

        # Create property fields
        row = 0

        # Ship ID (read-only)
        ttk.Label(self.properties_frame, text="Ship ID:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.properties_frame, width=PROPERTY_WIDTH, state="readonly").grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Ship Name
        ttk.Label(self.properties_frame, text="Name:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        name_var = tk.StringVar(value=ship.name)
        ttk.Entry(self.properties_frame, textvariable=name_var, width=PROPERTY_WIDTH).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Ship Class
        ttk.Label(self.properties_frame, text="Class:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        class_var = tk.StringVar(value=ship.ship_class)
        classes = [CLASS_FIGHTER, CLASS_FREIGHTER, CLASS_TRANSPORT, CLASS_CORVETTE, CLASS_FRIGATE,
                  CLASS_CRUISER, CLASS_DESTROYER, CLASS_CARRIER, CLASS_BATTLESHIP, CLASS_DREADNOUGHT]
        ttk.Combobox(self.properties_frame, textvariable=class_var, values=classes, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Ship Size
        ttk.Label(self.properties_frame, text="Size:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        size_var = tk.StringVar(value=ship.size)
        sizes = [SIZE_SMALL, SIZE_MEDIUM, SIZE_LARGE, SIZE_CAPITAL]
        ttk.Combobox(self.properties_frame, textvariable=size_var, values=sizes, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Outfit Space
        ttk.Label(self.properties_frame, text="Outfit Space:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        outfit_space_var = tk.IntVar(value=ship.outfit_space)
        ttk.Spinbox(self.properties_frame, from_=0, to=1000, textvariable=outfit_space_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Cargo Space
        ttk.Label(self.properties_frame, text="Cargo Space:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        cargo_space_var = tk.IntVar(value=ship.cargo_space)
        ttk.Spinbox(self.properties_frame, from_=0, to=1000, textvariable=cargo_space_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Turn Rate
        ttk.Label(self.properties_frame, text="Turn Rate:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        turn_rate_var = tk.DoubleVar(value=ship.turn_rate)
        ttk.Spinbox(self.properties_frame, from_=0.0, to=10.0, increment=0.1, textvariable=turn_rate_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Acceleration
        ttk.Label(self.properties_frame, text="Acceleration:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        acceleration_var = tk.DoubleVar(value=ship.acceleration)
        ttk.Spinbox(self.properties_frame, from_=0.0, to=10.0, increment=0.1, textvariable=acceleration_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Max Speed
        ttk.Label(self.properties_frame, text="Max Speed:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        max_speed_var = tk.DoubleVar(value=ship.max_speed)
        ttk.Spinbox(self.properties_frame, from_=0.0, to=20.0, increment=0.1, textvariable=max_speed_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Shields
        ttk.Label(self.properties_frame, text="Shields:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        shields_var = tk.IntVar(value=ship.shields)
        ttk.Spinbox(self.properties_frame, from_=0, to=1000, textvariable=shields_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Armor
        ttk.Label(self.properties_frame, text="Armor:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        armor_var = tk.IntVar(value=ship.armor)
        ttk.Spinbox(self.properties_frame, from_=0, to=1000, textvariable=armor_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Description
        ttk.Label(self.properties_frame, text="Description:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        description_var = tk.StringVar(value=ship.description)
        ttk.Entry(self.properties_frame, textvariable=description_var, width=PROPERTY_WIDTH).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Image Path
        ttk.Label(self.properties_frame, text="Image Path:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        image_path_var = tk.StringVar(value=ship.image_path if ship.image_path else "")
        ttk.Entry(self.properties_frame, textvariable=image_path_var, width=PROPERTY_WIDTH).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Store the variables for later use
        self.ship_properties[ship_id] = {
            'name': name_var,
            'ship_class': class_var,
            'size': size_var,
            'outfit_space': outfit_space_var,
            'cargo_space': cargo_space_var,
            'turn_rate': turn_rate_var,
            'acceleration': acceleration_var,
            'max_speed': max_speed_var,
            'shields': shields_var,
            'armor': armor_var,
            'description': description_var,
            'image_path': image_path_var
        }

    def update_preview(self, ship_id):
        """Update the preview for the selected ship."""
        if ship_id not in self.ships:
            return

        ship = self.ships[ship_id]

        # Clear the canvas
        self.preview_canvas.delete("all")

        # Try to load the ship's spritesheet
        spritesheet = load_sprite("ship", ship_id, ship.size)

        if spritesheet and spritesheet.image:
            # Use the first frame from the spritesheet
            frame = spritesheet.get_frame(0)
            if frame:
                # Convert Pygame surface to PIL Image
                mode = 'RGBA'
                size = frame.get_size()
                data = pg.image.tostring(frame, mode)
                pil_image = Image.frombytes(mode, size, data)

                # Resize to fit the preview canvas
                max_size = PREVIEW_SIZE - 20
                pil_image.thumbnail((max_size, max_size))

                # Convert to PhotoImage for Tkinter
                self.preview_image = ImageTk.PhotoImage(pil_image)

                # Display in the canvas
                self.preview_canvas.create_image(
                    PREVIEW_SIZE // 2, PREVIEW_SIZE // 2,
                    image=self.preview_image
                )

                # Add a label with the ship name
                self.preview_canvas.create_text(
                    PREVIEW_SIZE // 2, PREVIEW_SIZE - 10,
                    text=ship.name,
                    fill="white",
                    font=("Arial", 12)
                )
                return

        # If no spritesheet, try to use the ship's image
        if ship.image:
            # Convert Pygame surface to PIL Image
            mode = 'RGBA'
            size = ship.image.get_size()
            data = pg.image.tostring(ship.image, mode)
            pil_image = Image.frombytes(mode, size, data)

            # Resize to fit the preview canvas
            max_size = PREVIEW_SIZE - 20
            pil_image.thumbnail((max_size, max_size))

            # Convert to PhotoImage for Tkinter
            self.preview_image = ImageTk.PhotoImage(pil_image)

            # Display in the canvas
            self.preview_canvas.create_image(
                PREVIEW_SIZE // 2, PREVIEW_SIZE // 2,
                image=self.preview_image
            )
        else:
            # Draw a placeholder
            self.preview_canvas.create_text(
                PREVIEW_SIZE // 2, PREVIEW_SIZE // 2,
                text=f"No image for {ship.name}",
                fill="white",
                font=("Arial", 14)
            )

    def create_new(self):
        """Create a new ship."""
        # Ask for a ship ID
        ship_id = simpledialog.askstring("New Ship", "Enter a unique ID for the new ship:")

        if not ship_id:
            return

        if ship_id in self.ships:
            messagebox.showerror("Error", f"A ship with ID '{ship_id}' already exists")
            return

        # Create a new ship with default values
        self.ships[ship_id] = Ship(
            name=ship_id.capitalize(),
            ship_class=CLASS_FIGHTER,
            size=SIZE_SMALL,
            outfit_space=20,
            cargo_space=5,
            turn_rate=1.5,
            acceleration=0.5,
            max_speed=4.0,
            shields=50,
            armor=30,
            description=f"New {ship_id.capitalize()} ship"
        )

        # Add to the listbox
        self.listbox.insert(tk.END, ship_id)

        # Select the new ship
        self.listbox.selection_clear(0, tk.END)
        self.listbox.selection_set(tk.END)
        self.listbox.see(tk.END)
        self.listbox.event_generate("<<ListboxSelect>>")

    def delete_object(self, ship_id):
        """Delete a ship."""
        if ship_id in self.ships:
            del self.ships[ship_id]

            # Remove from the listbox
            for i in range(self.listbox.size()):
                if self.listbox.get(i) == ship_id:
                    self.listbox.delete(i)
                    break

            # Clear the properties
            if ship_id in self.ship_properties:
                del self.ship_properties[ship_id]

            # Clear the preview
            self.preview_canvas.delete("all")

    def duplicate_object(self, ship_id):
        """Duplicate a ship."""
        if ship_id not in self.ships:
            return

        # Ask for a new ID
        new_id = simpledialog.askstring("Duplicate Ship", "Enter a unique ID for the duplicated ship:", initialvalue=f"{ship_id}_copy")

        if not new_id:
            return

        if new_id in self.ships:
            messagebox.showerror("Error", f"A ship with ID '{new_id}' already exists")
            return

        # Create a copy of the ship
        original_ship = self.ships[ship_id]
        self.ships[new_id] = Ship(
            name=f"{original_ship.name} Copy",
            ship_class=original_ship.ship_class,
            size=original_ship.size,
            outfit_space=original_ship.outfit_space,
            cargo_space=original_ship.cargo_space,
            turn_rate=original_ship.turn_rate,
            acceleration=original_ship.acceleration,
            max_speed=original_ship.max_speed,
            shields=original_ship.shields,
            armor=original_ship.armor,
            description=original_ship.description,
            image_path=original_ship.image_path
        )

        # Add to the listbox
        self.listbox.insert(tk.END, new_id)

        # Select the new ship
        self.listbox.selection_clear(0, tk.END)
        self.listbox.selection_set(tk.END)
        self.listbox.see(tk.END)
        self.listbox.event_generate("<<ListboxSelect>>")


class PlanetEditor(EditorTab):
    """Editor for planet objects."""

    def __init__(self, parent):
        super().__init__(parent)
        self.planets = {}
        self.planet_properties = {}

    def load_data(self):
        """Load planet data."""
        # For planets, we don't have a global dictionary like SHIPS
        # We'll create a sample list for now
        self.planets = {
            "earth": {
                "name": "Earth",
                "faction": "Federation",
                "tech_level": 4,
                "planet_type": "terrestrial",
                "variant": 1,
                "is_landable": True
            },
            "mars": {
                "name": "Mars",
                "faction": "Federation",
                "tech_level": 3,
                "planet_type": "desert",
                "variant": 2,
                "is_landable": True
            },
            "jupiter": {
                "name": "Jupiter",
                "faction": "Independent",
                "tech_level": 0,
                "planet_type": "gas_giant",
                "variant": 1,
                "is_landable": False
            }
        }

        # Clear the listbox
        self.listbox.delete(0, tk.END)

        # Add planets to the listbox
        for planet_id in sorted(self.planets.keys()):
            self.listbox.insert(tk.END, planet_id)

    def save_data(self):
        """Save planet data."""
        # This would write the updated planet data back to a file
        messagebox.showinfo("Save Planets", "Planet data saving not yet implemented")

    def load_properties(self, planet_id):
        """Load properties for the selected planet."""
        if planet_id not in self.planets:
            return

        planet = self.planets[planet_id]

        # Create property fields
        row = 0

        # Planet ID (read-only)
        ttk.Label(self.properties_frame, text="Planet ID:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        ttk.Entry(self.properties_frame, width=PROPERTY_WIDTH, state="readonly").grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Planet Name
        ttk.Label(self.properties_frame, text="Name:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        name_var = tk.StringVar(value=planet["name"])
        ttk.Entry(self.properties_frame, textvariable=name_var, width=PROPERTY_WIDTH).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Faction
        ttk.Label(self.properties_frame, text="Faction:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        faction_var = tk.StringVar(value=planet["faction"])
        ttk.Entry(self.properties_frame, textvariable=faction_var, width=PROPERTY_WIDTH).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Tech Level
        ttk.Label(self.properties_frame, text="Tech Level:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        tech_level_var = tk.IntVar(value=planet["tech_level"])
        ttk.Spinbox(self.properties_frame, from_=TECH_LEVEL_MIN, to=TECH_LEVEL_MAX, textvariable=tech_level_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Planet Type
        ttk.Label(self.properties_frame, text="Planet Type:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        planet_type_var = tk.StringVar(value=planet["planet_type"])
        ttk.Combobox(self.properties_frame, textvariable=planet_type_var, values=PLANET_TYPES, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Variant
        ttk.Label(self.properties_frame, text="Variant:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        variant_var = tk.IntVar(value=planet["variant"])
        ttk.Spinbox(self.properties_frame, from_=1, to=10, textvariable=variant_var, width=PROPERTY_WIDTH-3).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Is Landable
        ttk.Label(self.properties_frame, text="Landable:").grid(row=row, column=0, sticky=tk.W, padx=5, pady=2)
        is_landable_var = tk.BooleanVar(value=planet["is_landable"])
        ttk.Checkbutton(self.properties_frame, variable=is_landable_var).grid(row=row, column=1, sticky=tk.W, padx=5, pady=2)
        row += 1

        # Store the variables for later use
        self.planet_properties[planet_id] = {
            'name': name_var,
            'faction': faction_var,
            'tech_level': tech_level_var,
            'planet_type': planet_type_var,
            'variant': variant_var,
            'is_landable': is_landable_var
        }

    def update_preview(self, planet_id):
        """Update the preview for the selected planet."""
        if planet_id not in self.planets:
            return

        planet = self.planets[planet_id]

        # Clear the canvas
        self.preview_canvas.delete("all")

        # Try to load the planet's spritesheet
        sprite_id = f"{planet['planet_type']}_{planet['variant']}"
        spritesheet = load_sprite("planet", sprite_id)

        if spritesheet and spritesheet.image:
            # Use the first frame from the spritesheet
            frame = spritesheet.get_frame(0)
            if frame:
                # Convert Pygame surface to PIL Image
                mode = 'RGBA'
                size = frame.get_size()
                data = pg.image.tostring(frame, mode)
                pil_image = Image.frombytes(mode, size, data)

                # Resize to fit the preview canvas
                max_size = PREVIEW_SIZE - 20
                pil_image.thumbnail((max_size, max_size))

                # Convert to PhotoImage for Tkinter
                self.preview_image = ImageTk.PhotoImage(pil_image)

                # Display in the canvas
                self.preview_canvas.create_image(
                    PREVIEW_SIZE // 2, PREVIEW_SIZE // 2,
                    image=self.preview_image
                )

                # Add a label with the planet name
                self.preview_canvas.create_text(
                    PREVIEW_SIZE // 2, PREVIEW_SIZE - 10,
                    text=planet["name"],
                    fill="white",
                    font=("Arial", 12)
                )
                return

        # Draw a placeholder
        color = "blue"
        if planet["planet_type"] == "desert":
            color = "orange"
        elif planet["planet_type"] == "ice":
            color = "cyan"
        elif planet["planet_type"] == "gas_giant":
            color = "yellow"
        elif planet["planet_type"] == "volcanic":
            color = "red"

        self.preview_canvas.create_oval(
            PREVIEW_SIZE // 4, PREVIEW_SIZE // 4,
            PREVIEW_SIZE * 3 // 4, PREVIEW_SIZE * 3 // 4,
            fill=color, outline="white"
        )

        self.preview_canvas.create_text(
            PREVIEW_SIZE // 2, PREVIEW_SIZE // 2,
            text=planet["name"],
            fill="white",
            font=("Arial", 14)
        )


class OutfitEditor(EditorTab):
    """Editor for outfit objects."""

    def __init__(self, parent):
        super().__init__(parent)
        self.outfits = {}
        self.outfit_properties = {}

    def load_data(self):
        """Load outfit data."""
        self.outfits = OUTFITS.copy()

        # Clear the listbox
        self.listbox.delete(0, tk.END)

        # Add outfits to the listbox
        for outfit_id in sorted(self.outfits.keys()):
            self.listbox.insert(tk.END, outfit_id)

    def save_data(self):
        """Save outfit data."""
        # This would write the updated outfit data back to the outfits.py file
        messagebox.showinfo("Save Outfits", "Outfit data saving not yet implemented")


class StationEditor(EditorTab):
    """Editor for station objects."""

    def __init__(self, parent):
        super().__init__(parent)
        self.stations = {}
        self.station_properties = {}

    def load_data(self):
        """Load station data."""
        # For stations, we don't have a global dictionary yet
        # We'll create a sample list for now
        self.stations = {
            "orbital_station": {
                "name": "Orbital Station",
                "faction": "Federation",
                "tech_level": 4,
                "station_type": "orbital",
                "variant": 1,
                "services": ["trade", "outfitter", "shipyard", "missions"]
            },
            "military_outpost": {
                "name": "Military Outpost",
                "faction": "Federation",
                "tech_level": 3,
                "station_type": "military",
                "variant": 1,
                "services": ["outfitter", "missions"]
            },
            "trading_post": {
                "name": "Trading Post",
                "faction": "Independent",
                "tech_level": 2,
                "station_type": "trading",
                "variant": 1,
                "services": ["trade", "missions"]
            }
        }

        # Clear the listbox
        self.listbox.delete(0, tk.END)

        # Add stations to the listbox
        for station_id in sorted(self.stations.keys()):
            self.listbox.insert(tk.END, station_id)

    def save_data(self):
        """Save station data."""
        # This would write the updated station data back to a file
        messagebox.showinfo("Save Stations", "Station data saving not yet implemented")


if __name__ == "__main__":
    app = GameEditor()
    app.mainloop()
