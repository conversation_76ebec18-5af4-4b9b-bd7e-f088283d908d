"""
Simple test for the modernized outfitter
Run this from the EscapeVelocityPy directory with: python test_outfitter_simple.py
"""

import os
import sys

# Change to the src directory to run the game
os.chdir('src')
sys.path.insert(0, '.')

# Now try to run the main game
try:
    import main
    print("✅ Successfully imported main game!")
    print("🎮 To test the outfitter:")
    print("   1. Run the game: python main.py")
    print("   2. Start a new game")
    print("   3. Dock at any planet (press L when near)")
    print("   4. Press O to open the outfitter")
    print("   5. Enjoy the new modernized interface!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the EscapeVelocityPy directory")
