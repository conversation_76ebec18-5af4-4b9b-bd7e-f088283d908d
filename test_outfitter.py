"""
Test script to verify the modernized outfitter works
Run this to test the outfitter without running the full game
"""

import pygame as pg
import sys
import os

# Add the src directory to the path so we can import game modules
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

try:
    from game_objects.modernized_outfitter import ModernizedOutfitter
    from game_objects.standardized_outfits import OUTFITS_REGISTRY
    from game_objects.example_outfits import *  # Load example outfits
    from game_objects.player import Player
    from game_objects.planet import Planet
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    print(f"Project root: {project_root}")
    print(f"Src path: {src_path}")
    sys.exit(1)

class MockGame:
    """Mock game class for testing the outfitter."""
    
    def __init__(self):
        pg.init()
        self.screen = pg.display.set_mode((1200, 800))
        pg.display.set_caption("Modernized Outfitter Test")
        self.clock = pg.time.Clock()
        self.running = True
        self.state = "TESTING"
        
        # Create a mock player with a scout ship
        self.player = Player(self, "Test Pilot", "Test Ship", "scout")
        self.player.credits = 50000  # Give player some credits
        
        # Create a mock planet with high tech level
        self.docked_planet = Planet(self, 400, 300, "Test Station", 
                                   faction="federation", tech_level=5)
        
        print(f"Available outfits in registry: {len(OUTFITS_REGISTRY)}")
        for outfit_id, outfit in OUTFITS_REGISTRY.items():
            print(f"  {outfit_id}: {outfit.name} ({outfit.category})")
    
    def set_status_message(self, message, color=(255, 255, 255)):
        """Mock status message method."""
        print(f"Status: {message}")

def test_outfitter():
    """Test the modernized outfitter."""
    game = MockGame()
    outfitter = ModernizedOutfitter(game)
    
    print("Testing modernized outfitter...")
    print(f"Player credits: {game.player.credits}")
    print(f"Planet tech level: {game.docked_planet.tech_level}")
    
    # Open the outfitter
    if outfitter.open(game.docked_planet):
        print("Outfitter opened successfully!")
        
        # Run the outfitter interface
        result = outfitter.run(game.screen)
        print(f"Outfitter closed with result: {result}")
    else:
        print("Failed to open outfitter!")
    
    pg.quit()

if __name__ == "__main__":
    test_outfitter()
