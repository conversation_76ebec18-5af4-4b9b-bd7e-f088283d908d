"""
Updated AI ship to work with the new standardized outfit system - Complete Version
"""
import pygame as pg
import random
import math
from game_objects.ships import get_ship_by_id
from game_objects.standardized_outfits import *
from game_objects.example_outfits import *  # Load example outfits
from game_objects.projectiles import LaserProjectile, MissileProjectile
from game_objects.sprite_manager import load_sprite

# --- AI Ship Constants ---
AI_DRAG_FACTOR = 0.015
AI_SENSOR_RANGE = 400
AI_PATROL_RADIUS = 300
AI_WEAPON_RANGE = 300

# --- AI States ---
AI_STATE_IDLE = "IDLE"
AI_STATE_PATROLLING = "PATROLLING"
AI_STATE_ATTACKING = "ATTACKING"
AI_STATE_FLEEING = "FLEEING"
AI_STATE_TRADING = "TRADING"
AI_STATE_DISABLED = "DISABLED"

class AIShip(pg.sprite.Sprite):
    def __init__(self, game, pos_x, pos_y, faction_id, ship_id="scout"):
        super().__init__()
        self.game = game
        self.faction_id = faction_id  # Set faction_id FIRST!

        # Get the ship from the ships module
        self.ship = get_ship_by_id(ship_id)
        if not self.ship:
            self.ship = get_ship_by_id("scout")

        self.ship_id = ship_id

        # Try to load the ship's spritesheet
        print(f"AI Ship: Attempting to load spritesheet for ship: {ship_id} (size: {self.ship.size})")
        self.spritesheet = load_sprite("ship", ship_id, self.ship.size)

        # Set up the ship image
        if self.spritesheet and self.spritesheet.image:
            print(f"AI Ship: Using spritesheet for ship: {ship_id}")
            self.image_orig = self.spritesheet.get_frame(0)
            if not self.image_orig:
                print(f"AI Ship: Failed to get frame 0 from spritesheet for ship: {ship_id}")
                if self.ship.image:
                    print(f"AI Ship: Falling back to ship's static image")
                    self.image_orig = self.ship.image
                else:
                    print(f"AI Ship: Creating default image for ship: {ship_id}")
                    self._create_default_image()
        elif self.ship.image:
            print(f"AI Ship: No spritesheet found, using ship's static image for: {ship_id}")
            self.image_orig = self.ship.image
        else:
            print(f"AI Ship: No spritesheet or static image found, creating default for: {ship_id}")
            self._create_default_image()

        self.image_orig.set_colorkey((0, 0, 0))
        self.image = self.image_orig.copy()
        self.rect = self.image.get_rect()

        self.pos = pg.math.Vector2(pos_x, pos_y)
        self.rect.center = self.pos
        self.vel = pg.math.Vector2(random.uniform(-1,1), random.uniform(-1,1)).normalize() * random.uniform(0, self.ship.max_speed/2)
        self.angle = random.uniform(0, 360)
        self.target_angle = self.angle
        self.rotate_image()

        self.ship_type = self.ship.ship_class
        self.ai_state = AI_STATE_PATROLLING
        self.target_entity = None
        self.patrol_target_pos = None
        self.state_timer = 0

        # Ship stats from base ship
        self.base_max_shields = self.ship.max_shields
        self.base_shields = self.ship.shields
        self.base_max_armor = self.ship.max_armor
        self.base_armor = self.ship.armor
        self.base_acceleration = self.ship.acceleration
        self.base_turn_rate = self.ship.turn_rate
        self.base_max_speed = self.ship.max_speed

        # Current stats (will be modified by outfits)
        self.max_shields = self.base_max_shields
        self.shields = self.base_shields
        self.max_armor = self.base_max_armor
        self.armor = self.base_armor  # Add this line!
        self.health = self.base_armor  # AI ships use 'health' for armor
        self.max_health = self.base_max_armor
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed
        
        # NEW: Power and Fuel Systems from ship
        self.power_capacity = self.ship.power_capacity
        self.power = self.power_capacity
        self.power_regen_rate = self.ship.power_regen_rate
        self.fuel_capacity = self.ship.fuel_capacity
        self.fuel = self.fuel_capacity

        # Outfit system
        self.installed_outfits = {}
        self.used_outfit_space = 0

        self.shield_recharge_delay = 180
        self.shield_recharge_timer = 0

        # Weapon systems - NEW STANDARDIZED SYSTEM
        self.weapons = []
        self.active_weapon_index = 0
        self.projectiles = pg.sprite.Group()
        self.last_fire_time = 0
        self.weapon_range = AI_WEAPON_RANGE

        # Equip AI with appropriate weapons based on ship size and faction
        self._equip_ai_weapons()

    def _create_default_image(self):
        """Create a default image for the ship."""
        self.image_orig = pg.Surface([30, 20], pg.SRCALPHA)
        color = (200, 200, 0)  # Default yellow
        if self.faction_id == "pirates":
            color = (200, 0, 0)  # Red for pirates
        elif self.faction_id == "federation":
            color = (0, 0, 200)  # Blue for federation
        elif self.faction_id == "corporation":
            color = (0, 200, 0)  # Green for corporation

        pg.draw.polygon(self.image_orig, color, [(15, 0), (0, 20), (30, 20)])

    def _equip_ai_weapons(self):
        """Equip AI ship with weapons appropriate to its size and faction."""
        # Basic weapons for all AI ships
        if self.ship.size == "small":
            self.install_outfit("pulse_laser")
            if random.random() < 0.3:  # 30% chance for missiles
                self.install_outfit("missile_rack")
                self.load_ammo("light_missile")
        elif self.ship.size == "medium":
            self.install_outfit("laser_cannon")
            if random.random() < 0.5:  # 50% chance for missiles
                self.install_outfit("missile_rack")
                self.load_ammo("guided_missile")
            if random.random() < 0.3:  # 30% chance for turret
                self.install_outfit("laser_turret")
        elif self.ship.size == "large":
            self.install_outfit("heavy_laser")
            self.install_outfit("laser_turret")
            if random.random() < 0.7:  # 70% chance for heavy missiles
                self.install_outfit("heavy_launcher")
                self.load_ammo("heavy_missile")
        elif self.ship.size == "capital":
            self.install_outfit("heavy_laser")
            self.install_outfit("laser_turret")
            self.install_outfit("torpedo_tube")
            self.load_ammo("torpedo")

        # Add some defense
        if self.ship.size in ["medium", "large", "capital"]:
            self.install_outfit("basic_shield")
        if self.ship.size in ["large", "capital"]:
            self.install_outfit("armor_plating")

    def install_outfit(self, outfit_id):
        """Install an outfit on the AI ship."""
        outfit_template = get_outfit_by_id(outfit_id)
        if not outfit_template:
            return False

        if not outfit_template.can_install_on_ship(self.ship):
            return False

        if self.used_outfit_space + outfit_template.space_required > self.ship.outfit_space:
            return False

        # Install the outfit
        if outfit_id in self.installed_outfits:
            self.installed_outfits[outfit_id] += 1
        else:
            self.installed_outfits[outfit_id] = 1

        self.used_outfit_space += outfit_template.space_required
        self._recalculate_ship_stats()
        return True

    def load_ammo(self, ammo_id):
        """Load ammunition into compatible weapons."""
        ammo_template = get_outfit_by_id(ammo_id)
        if not ammo_template or not isinstance(ammo_template, Ammunition):
            return False

        for weapon in self.weapons:
            if weapon.uses_ammo and weapon.ammo_type == ammo_template.ammo_type:
                ammo_to_load = min(ammo_template.quantity, weapon.max_ammo - weapon.current_ammo)
                weapon.current_ammo += ammo_to_load
        return True

    def _recalculate_ship_stats(self):
        """Recalculate ship stats based on installed outfits."""
        # Reset to base values
        self.max_shields = self.base_max_shields
        self.shields = min(self.shields, self.max_shields)
        self.max_armor = self.base_max_armor
        self.armor = min(self.armor, self.max_armor)  # Add this line!
        self.health = min(self.health, self.max_armor)
        self.max_health = self.max_armor
        self.acceleration = self.base_acceleration
        self.turn_rate = self.base_turn_rate
        self.max_speed = self.base_max_speed

        # Clear weapons list
        self.weapons = []

        # Apply effects from all installed outfits
        for outfit_id, quantity in self.installed_outfits.items():
            outfit_template = get_outfit_by_id(outfit_id)
            if not outfit_template:
                continue

            for _ in range(quantity):
                outfit_instance = outfit_template.clone()

                if isinstance(outfit_instance, Weapon):
                    self.weapons.append(outfit_instance)
                elif isinstance(outfit_instance, DefenseOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, EngineOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, ElectronicsOutfit):
                    outfit_instance.apply_effects(self)
                elif isinstance(outfit_instance, UtilityOutfit):
                    outfit_instance.apply_effects(self)

        # Ensure values don't go below minimum thresholds
        self.acceleration = max(0.05, self.acceleration)
        self.turn_rate = max(0.05, self.turn_rate)
        self.max_speed = max(1.0, self.max_speed)

    def turn_toward_angle(self, target_angle):
        """Gradually turn toward a target angle using the ship's turn rate."""
        current_angle = self.angle % 360
        target_angle = target_angle % 360

        angle_diff = target_angle - current_angle

        if angle_diff > 180:
            angle_diff -= 360
        elif angle_diff < -180:
            angle_diff += 360

        if abs(angle_diff) > 2:
            turn_amount = min(abs(angle_diff), self.turn_rate)
            if angle_diff > 0:
                self.angle += turn_amount
            else:
                self.angle -= turn_amount

            self.angle = self.angle % 360
            return False
        else:
            self.angle = target_angle
            return True

    def get_angle_to_point(self, target_pos):
        """Calculate the angle needed to face a target position."""
        direction = target_pos - self.pos
        if direction.length_squared() > 0:
            angle = direction.angle_to(pg.math.Vector2(0, -1))
            return angle % 360
        return self.angle

    def apply_thrust_in_facing_direction(self, thrust_multiplier=1.0):
        """Apply thrust in the direction the ship is currently facing."""
        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))
        thrust = forward * self.acceleration * thrust_multiplier
        self.vel += thrust

    def fire_weapon(self, dt):
        """Fire the currently active weapon if possible."""
        if not self.weapons or self.active_weapon_index >= len(self.weapons):
            return None

        weapon = self.weapons[self.active_weapon_index]
        weapon.update(dt)

        if not weapon.can_fire():
            return None
        
        # NEW: Check power requirements for AI ships
        power_cost = getattr(weapon, 'power_cost', self.ship.weapon_power_cost_base)
        if not self.ship.consume_power(power_cost):
            return None  # AI ships just don't fire if no power

        target = self.target_entity
        if weapon.mount_type in [MOUNT_TYPE_TURRET, MOUNT_TYPE_GUIDED] and not target:
            return None

        # Calculate spawn position and angle
        spawn_pos = self.pos.copy()
        projectile_angle = self.angle

        forward_angle = math.radians(self.angle - 90)
        forward = pg.math.Vector2(math.cos(forward_angle), math.sin(forward_angle))

        if weapon.mount_type == MOUNT_TYPE_FIXED:
            offset_to_nose = self.image_orig.get_height() / 2
            spawn_pos = self.pos + forward * offset_to_nose
        elif weapon.mount_type == MOUNT_TYPE_TURRET and target:
            to_target = target.pos - self.pos
            target_angle = math.degrees(math.atan2(to_target.y, to_target.x)) + 90
            projectile_angle = target_angle % 360
            to_target.normalize()
            spawn_pos = self.pos + to_target * (self.rect.width / 3)

        # Create projectile based on weapon behavior
        if weapon.projectile_behavior == BEHAVIOR_INSTANT:
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )
        elif weapon.uses_ammo:
            projectile = MissileProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range,
                target,
                tracking=getattr(weapon, 'tracking_strength', 0.0)
            )
            weapon.current_ammo -= 1
        else:
            projectile = LaserProjectile(
                self.game,
                self,
                spawn_pos,
                projectile_angle,
                weapon.damage,
                weapon.range
            )

        if weapon.fire():
            self.game.all_sprites.add(projectile)
            self.projectiles.add(projectile)
            return projectile

        return None

    def rotate_image(self):
        """Update the ship's image based on its current angle."""
        old_center = self.rect.center

        try:
            if self.spritesheet and self.spritesheet.image:
                frame = self.spritesheet.get_frame_by_angle(self.angle)
                if frame:
                    self.image = frame
                else:
                    self.image = pg.transform.rotate(self.image_orig, -self.angle)
            else:
                self.image = pg.transform.rotate(self.image_orig, -self.angle)
        except Exception as e:
            print(f"AI Ship: Error during rotation: {e}")
            self.image = self.image_orig.copy()

        self.rect = self.image.get_rect()
        self.rect.center = old_center

    def update(self, dt=1/60):
        """Update AI ship state."""
        self.state_timer -= 1
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            # NEW: Check power for shield regeneration
            shield_power_cost = self.ship.shield_regen_power_cost * dt
            if self.ship.consume_power(shield_power_cost):
                self.shields = min(self.max_shields, self.shields + 0.1)
        
        # NEW: Power regeneration
        self.ship.regenerate_power(dt)

        # State Machine
        if self.ai_state == AI_STATE_IDLE:
            self.perform_idle()
        elif self.ai_state == AI_STATE_PATROLLING:
            self.perform_patrolling()
        elif self.ai_state == AI_STATE_ATTACKING:
            self.perform_attacking(dt)
        elif self.ai_state == AI_STATE_FLEEING:
            self.perform_fleeing()
        elif self.ai_state == AI_STATE_DISABLED:
            self.perform_disabled()

        # Update all weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Update projectiles
        for projectile in list(self.projectiles):
            if projectile.update(dt):
                projectile.kill()

        self.apply_movement()
        self.rotate_image()

    def apply_movement(self):
        # Apply drag
        if self.vel.length_squared() > 0:
            drag = self.vel.normalize() * AI_DRAG_FACTOR * self.vel.length_squared()
            if drag.length_squared() < self.vel.length_squared():
                self.vel -= drag
            else:
                self.vel = pg.math.Vector2(0,0)

        # Limit speed
        if self.vel.length_squared() > self.max_speed * self.max_speed:
            self.vel.scale_to_length(self.max_speed)

        self.pos += self.vel
        self.rect.center = self.pos

        # World bounds
        if self.pos.x > self.game.camera.width: self.pos.x = 0
        if self.pos.x < 0: self.pos.x = self.game.camera.width
        if self.pos.y > self.game.camera.height: self.pos.y = 0
        if self.pos.y < 0: self.pos.y = self.game.camera.height
        self.rect.center = self.pos

    def perform_idle(self):
        if self.state_timer <= 0:
            self.ai_state = AI_STATE_PATROLLING
            self.state_timer = random.randint(120, 300)

    def perform_patrolling(self):
        if self.state_timer <= 0 or self.patrol_target_pos is None or self.pos.distance_to(self.patrol_target_pos) < 50:
            self.patrol_target_pos = pg.math.Vector2(
                random.randrange(100, self.game.camera.width - 100),
                random.randrange(100, self.game.camera.height - 100)
            )
            self.state_timer = random.randint(300, 600)

        if self.patrol_target_pos:
            target_angle = self.get_angle_to_point(self.patrol_target_pos)
            facing_target = self.turn_toward_angle(target_angle)

            current_direction = self.patrol_target_pos - self.pos
            if current_direction.length() > 50:
                angle_diff = abs((self.angle - target_angle) % 360)
                if angle_diff > 180:
                    angle_diff = 360 - angle_diff

                if angle_diff < 30:
                    thrust_multiplier = max(0.3, 1.0 - angle_diff / 30.0)
                    self.apply_thrust_in_facing_direction(thrust_multiplier)

        if self.state_timer % 60 == 0:
            self.scan_for_targets()

    def perform_attacking(self, dt=1/60):
        if not self.target_entity or not self.target_entity.alive() or self.health <= 0:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None
            return

        direction_to_target = (self.target_entity.pos - self.pos)
        distance = direction_to_target.length()

        if distance > AI_SENSOR_RANGE * 1.5:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None
            return

        target_angle = self.get_angle_to_point(self.target_entity.pos)
        facing_target = self.turn_toward_angle(target_angle)

        if distance > 120:
            angle_diff = abs((self.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff < 45:
                thrust_multiplier = max(0.5, 1.0 - angle_diff / 45.0)
                self.apply_thrust_in_facing_direction(thrust_multiplier)

        elif distance < 80:
            self.apply_thrust_in_facing_direction(-0.3)

        if distance < self.weapon_range:
            angle_diff = abs((self.angle - target_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff < 20 and random.random() < 0.2:
                self.fire_weapon(dt)

    def perform_fleeing(self):
        if not self.target_entity or not self.target_entity.alive():
            self.ai_state = AI_STATE_PATROLLING
            return

        direction_from_target = (self.pos - self.target_entity.pos)
        if direction_from_target.length_squared() > 0:
            flee_angle = self.get_angle_to_point(self.pos + direction_from_target.normalize() * 100)

            self.turn_toward_angle(flee_angle)

            angle_diff = abs((self.angle - flee_angle) % 360)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            if angle_diff < 60:
                self.apply_thrust_in_facing_direction(1.2)

        if self.pos.distance_to(self.target_entity.pos) > AI_SENSOR_RANGE * 2:
            self.ai_state = AI_STATE_PATROLLING
            self.target_entity = None

    def scan_for_targets(self):
        # Check player
        player = self.game.player
        if player and player.alive():
            dist_to_player = self.pos.distance_to(player.pos)
            if dist_to_player < AI_SENSOR_RANGE:
                relation_to_player = self.game.faction_relations.get(self.faction_id, {}).get(self.game.player_faction_id, 0.0)
                if relation_to_player < -0.5:
                    self.target_entity = player
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship attacking player!")
                    return

        # Check other AI ships
        for ship in self.game.ai_ships:
            if ship == self or not ship.alive(): continue
            dist_to_ship = self.pos.distance_to(ship.pos)
            if dist_to_ship < AI_SENSOR_RANGE:
                relation_to_other_ai = self.game.faction_relations.get(self.faction_id, {}).get(ship.faction_id, 0.0)
                if relation_to_other_ai < -0.5:
                    self.target_entity = ship
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship ({self.ship_type}) attacking {ship.faction_id} ship ({ship.ship_type})!")
                    return

    def take_damage(self, amount, attacker_faction_id="unknown"):
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.health += self.shields
                self.shields = 0
        else:
            self.health -= amount

        self.shield_recharge_timer = self.shield_recharge_delay

        if self.health <= 0:
            self.kill()
            print(f"{self.ship_type} ({self.faction_id}) destroyed!")
            self.create_explosion()
            self.drop_loot()
        elif self.health < self.max_health * 0.1:
            if self.ai_state != AI_STATE_DISABLED:
                self.ai_state = AI_STATE_DISABLED
                print(f"DEBUG: {self.ship_type} ({self.faction_id}) changed state to AI_STATE_DISABLED.")
                self.game.set_status_message(f"{self.ship_type} disabled! Press B to board.", (255, 255, 0))
        elif self.ai_state != AI_STATE_DISABLED:
            attacker_entity = None

            if attacker_faction_id == "player":
                attacker_entity = self.game.player
            else:
                for ship in self.game.ai_ships:
                    if ship.faction_id == attacker_faction_id:
                        attacker_entity = ship
                        break

            if attacker_entity:
                flee_threshold = 0.3
                if self.ship.size == "small":
                    flee_threshold = 0.4
                elif self.ship.size == "large" or self.ship.size == "capital":
                    flee_threshold = 0.2

                personality_factor = random.uniform(0.8, 1.2)
                flee_threshold *= personality_factor

                if self.health / self.max_health < flee_threshold and self.ai_state != AI_STATE_FLEEING:
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_FLEEING
                    print(f"{self.faction_id} ship fleeing from {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship fleeing!", (255, 255, 0))
                elif self.ai_state != AI_STATE_ATTACKING:
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship fighting back against {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship attacking!", (255, 100, 100))

    def create_explosion(self):
        """Create an explosion effect when the ship is destroyed."""
        print(f"Explosion at {self.pos}")

    def drop_loot(self):
        """Drop loot when the ship is destroyed."""
        base_loot_value = 100 * (1 + ["small", "medium", "large", "capital"].index(self.ship.size))
        loot_value = int(base_loot_value * random.uniform(0.5, 1.5))

        if hasattr(self.game, 'player') and self.game.player:
            self.game.player.credits += loot_value
            self.game.set_status_message(f"Collected {loot_value} credits from destroyed ship", (0, 255, 0))

        print(f"Dropped {loot_value} credits")

    def perform_disabled(self):
        """Behavior when ship is disabled."""
        if self.vel.length_squared() > 0:
            self.vel *= 0.95

        if random.random() < 0.05:
            print(f"Disabled ship {self.ship_type} emitting sparks")

        if hasattr(self.game, 'player') and self.game.player:
            dist_to_player = self.pos.distance_to(self.game.player.pos)
            if dist_to_player < 100:
                if random.random() < 0.1:
                    self.game.set_status_message(f"Press B to board disabled {self.ship_type}", (255, 255, 0))

        if self.shields < self.max_shields:
            self.shields = min(self.max_shields, self.shields + 0.01)
